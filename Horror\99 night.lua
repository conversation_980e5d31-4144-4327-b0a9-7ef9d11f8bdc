local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()

WindUI:SetTheme("Dark")

-- Version and Update Log System
local SCRIPT_VERSION = "1.2"
local UPDATE_LOG_KEY = "PulseHub_UpdateLog_" .. SCRIPT_VERSION

-- Check if this is the first time running this version
local function hasSeenUpdateLog()
    local success, result = pcall(function()
        return readfile("PulseHub/" .. UPDATE_LOG_KEY .. ".txt")
    end)
    return success and result == "seen"
end

local function markUpdateLogAsSeen()
    pcall(function()
        if not isfolder("PulseHub") then
            makefolder("PulseHub")
        end
        writefile("PulseHub/" .. UPDATE_LOG_KEY .. ".txt", "seen")
    end)
end

-- Show update log popup for first-time users
local function showUpdateLogPopup()
    local popup = WindUI:Popup({
        Title = "🎉 PulseHub Update Log v" .. SCRIPT_VERSION,
        Icon = "star",
        Content = [[Welcome to PulseHub v1.2!

🆕 What's New:

🚀 Teleport
• Teleport to Campfire, Camp, Houses, and more!
• Easy dropdown menu for quick travel

🖥️ UI Improvements
• Access the UI from anywhere in the game
• Better controls and design

💥 Hitbox Expander
• Modify hitboxes for wolves, rabbits, and more!
• Customizable sizes for better gameplay

🗺️ Map ESP
• See ALL objects on the map!
• Color-coded for easy identification

🏰 NEW Stronghold ESP!
• Auto-detects stronghold locations
• Refreshes every 5 seconds
• Get notified when strongholds appear

Thanks for using PulseHub! 🎮]],
        Buttons = {
            {
                Title = "Continue",
                Callback = function()
                    markUpdateLogAsSeen()
                    print("[PulseHub] Welcome! Update log acknowledged for version " .. SCRIPT_VERSION)
                end,
                Variant = "Primary"
            },
            {
                Title = "What's PulseHub?",
                Callback = function()
                    WindUI:Popup({
                        Title = "About PulseHub",
                        Icon = "info",
                        Content = [[PulseHub is a comprehensive game enhancement script designed to improve your gaming experience.

🎯 Our Mission:
Provide powerful, reliable, and user-friendly tools that enhance gameplay without compromising fairness.

🛡️ Safety First:
• Built with modern security practices
• Regular updates and maintenance
• Community-tested features

💡 Key Benefits:
• Enhanced survival capabilities
• Improved navigation and exploration
• Advanced visual assistance systems
• Streamlined user interface

Join thousands of players who trust PulseHub for their gaming adventures!]],
                        Buttons = {
                            {
                                Title = "Got it!",
                                Callback = function()
                                    markUpdateLogAsSeen()
                                end,
                                Variant = "Primary"
                            }
                        }
                    })
                end,
                Variant = "Secondary"
            }
        }
    })
end

local Players = game:GetService("Players")
local Mouse = Players.LocalPlayer:GetMouse()
local flyKeyDown, flyKeyUp
_G.FLYING = false
_G.flySpeed = 50

local function getRoot(char)
    return char:FindFirstChild('HumanoidRootPart') or char:FindFirstChild('Torso') or char:FindFirstChild('UpperTorso')
end

function sFLY(vfly)
    repeat wait() until Players.LocalPlayer and Players.LocalPlayer.Character and getRoot(Players.LocalPlayer.Character) and Players.LocalPlayer.Character:FindFirstChildOfClass("Humanoid")
    
    if flyKeyDown or flyKeyUp then 
        flyKeyDown:Disconnect() 
        flyKeyUp:Disconnect() 
    end

    local T = getRoot(Players.LocalPlayer.Character)
    local CONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
    local lCONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
    local SPEED = 0

    local function FLY()
        _G.FLYING = true
        local BG = Instance.new('BodyGyro')
        local BV = Instance.new('BodyVelocity')
        BG.P = 9e4
        BG.Parent = T
        BV.Parent = T
        BG.maxTorque = Vector3.new(9e9, 9e9, 9e9)
        BG.cframe = T.CFrame
        BV.velocity = Vector3.new(0, 0, 0)
        BV.maxForce = Vector3.new(9e9, 9e9, 9e9)
        task.spawn(function()
            repeat wait()
                if not vfly and Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid') then
                    Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid').PlatformStand = true
                end
                if CONTROL.L + CONTROL.R ~= 0 or CONTROL.F + CONTROL.B ~= 0 or CONTROL.Q + CONTROL.E ~= 0 then
                    SPEED = _G.flySpeed
                elseif not (CONTROL.L + CONTROL.R ~= 0 or CONTROL.F + CONTROL.B ~= 0 or CONTROL.Q + CONTROL.E ~= 0) and SPEED ~= 0 then
                    SPEED = 0
                end
                if (CONTROL.L + CONTROL.R) ~= 0 or (CONTROL.F + CONTROL.B) ~= 0 or (CONTROL.Q + CONTROL.E) ~= 0 then
                    BV.velocity = ((workspace.CurrentCamera.CoordinateFrame.lookVector * (CONTROL.F + CONTROL.B)) + ((workspace.CurrentCamera.CoordinateFrame * CFrame.new(CONTROL.L + CONTROL.R, (CONTROL.F + CONTROL.B + CONTROL.Q + CONTROL.E) * 0.2, 0).p) - workspace.CurrentCamera.CoordinateFrame.p)) * SPEED
                    lCONTROL = {F = CONTROL.F, B = CONTROL.B, L = CONTROL.L, R = CONTROL.R}
                elseif (CONTROL.L + CONTROL.R) == 0 and (CONTROL.F + CONTROL.B) == 0 and (CONTROL.Q + CONTROL.E) == 0 and SPEED ~= 0 then
                    BV.velocity = ((workspace.CurrentCamera.CoordinateFrame.lookVector * (lCONTROL.F + lCONTROL.B)) + ((workspace.CurrentCamera.CoordinateFrame * CFrame.new(lCONTROL.L + lCONTROL.R, (lCONTROL.F + lCONTROL.B + CONTROL.Q + CONTROL.E) * 0.2, 0).p) - workspace.CurrentCamera.CoordinateFrame.p)) * SPEED
                else
                    BV.velocity = Vector3.new(0, 0, 0)
                end
                BG.cframe = workspace.CurrentCamera.CoordinateFrame
            until not _G.FLYING
            CONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
            lCONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
            SPEED = 0
            BG:Destroy()
            BV:Destroy()
            if Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid') then
                Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid').PlatformStand = false
            end
        end)
    end
    
    flyKeyDown = Mouse.KeyDown:Connect(function(KEY)    
        if KEY:lower() == 'w' then
            CONTROL.F = _G.flySpeed
        elseif KEY:lower() == 's' then
            CONTROL.B = -_G.flySpeed
        elseif KEY:lower() == 'a' then
            CONTROL.L = -_G.flySpeed
        elseif KEY:lower() == 'd' then 
            CONTROL.R = _G.flySpeed
        elseif KEY:lower() == 'e' then
            CONTROL.Q = _G.flySpeed*2
        elseif KEY:lower() == 'q' then
            CONTROL.E = -_G.flySpeed*2
        end
        pcall(function() workspace.CurrentCamera.CameraType = Enum.CameraType.Track end)
    end)
    
    flyKeyUp = Mouse.KeyUp:Connect(function(KEY)
        if KEY:lower() == 'w' then
            CONTROL.F = 0
        elseif KEY:lower() == 's' then
            CONTROL.B = 0
        elseif KEY:lower() == 'a' then
            CONTROL.L = 0
        elseif KEY:lower() == 'd' then
            CONTROL.R = 0
        elseif KEY:lower() == 'e' then
            CONTROL.Q = 0
        elseif KEY:lower() == 'q' then
            CONTROL.E = 0
        end
    end)
    FLY()
end

-- Initialize Global Variables First
_G.PulseESP = _G.PulseESP or {}
_G.PulseESP.itemHighlights = {}
_G.PulseESP.playerHighlights = {}
_G.PulseESP.monsterHighlights = {}
_G.PulseESP.characterHighlights = {}
_G.PulseESP.gunsArmorHighlights = {}
_G.PulseESP.fuelHighlights = {}
_G.PulseESP.foodHighlights = {}
_G.PulseESP.craftingHighlights = {}
_G.PulseESP.landmarkHighlights = {}
_G.PulseESP.availableItems = {"ALL"}
_G.PulseESP.selectedItem = "ALL"
_G.PulseESP.availableCharacterGroups = {"ALL"}
_G.PulseESP.selectedCharacterGroup = "ALL"
_G.PulseESP.selectedGunsArmorESP = "ALL"
_G.PulseESP.selectedFuelESP = "ALL"
_G.PulseESP.selectedFoodESP = "ALL"
_G.PulseESP.selectedCraftingESP = "ALL"
_G.PulseESP.availableLandmarkGroups = {"ALL"}
_G.PulseESP.selectedLandmarkGroups = {}
_G.PulseESP.itemESPActive = false
_G.PulseESP.playerESPActive = false
_G.PulseESP.monsterESPActive = false
_G.PulseESP.characterESPActive = false
_G.PulseESP.gunsArmorESPActive = false
_G.PulseESP.fuelESPActive = false
_G.PulseESP.foodESPActive = false
_G.PulseESP.craftingESPActive = false
_G.PulseESP.mapESPActive = false
_G.PulseESP.renderDistance = 500
_G.PulseESP.espColor = Color3.fromRGB(0, 255, 0)
_G.PulseESP.activeThreads = {}

_G.PulseItems = _G.PulseItems or {}
_G.PulseItems.availableItems = {}
_G.PulseItems.selectedItems = {}
_G.PulseItems.bringAllItems = false
_G.PulseItems.bringSelectedItems = false
_G.PulseItems.bringDistance = 25
_G.PulseItems.maxItemsPerBatch = 15
_G.PulseItems.itemScanInterval = 3
_G.PulseItems.lastScanTime = 0
_G.PulseItems.activeThreads = {}
_G.PulseItems.excludeList = {"Player", "Character", "Humanoid"}
_G.PulseItems.previousStats = {}
_G.PulseItems.autoUpdateStats = true

_G.PulseCombat = _G.PulseCombat or {}
_G.PulseCombat.killAuraActive = false
_G.PulseCombat.killDistance = 125
_G.PulseCombat.activeThreads = {}
_G.PulseCombat.hitboxExtenderActive = false
_G.PulseCombat.wolfHitboxExtenderActive = false
_G.PulseCombat.hitboxSize = 20
_G.PulseCombat.wolfHitboxSize = 20
_G.PulseCombat.extendedHitboxes = {}

_G.PulseSurvival = _G.PulseSurvival or {}
_G.PulseSurvival.autoEatActive = false
_G.PulseSurvival.teleportToFireActive = false
_G.PulseSurvival.hungerThreshold = 0.7
_G.PulseSurvival.lastState = nil
_G.PulseSurvival.activeThreads = {}

_G.PulseCamera = _G.PulseCamera or {}
_G.PulseCamera.zoomEnabled = false
_G.PulseCamera.minZoom = 0.5
_G.PulseCamera.maxZoom = 400
_G.PulseCamera.activeThread = nil

_G.PulseProximity = _G.PulseProximity or {}
_G.PulseProximity.instantPromptsActive = false
_G.PulseProximity.holdDuration = 0.1
_G.PulseProximity.activeThread = nil
_G.PulseProximity.modifiedPrompts = {}
_G.PulseProximity.originalValues = {}

-- Item Arrays for Specialized ESP
local GUNS_ARMOR_ITEMS = { "Revolver", "Rifle", "Revolver Ammo", "Rifle Ammo", "Leather Body", "Iron Body", "Thorn Body" }
local CRAFTING_ITEMS = { "Bolt", "Sheet Metal", "Old Radio", "Broken Fan", "Broken Microwave" }
local FUEL_ITEMS = { "Log", "Chair", "Coal", "Fuel Canister", "Oil Barrel", "Biofuel" }
local FOOD_ITEMS = { "Carrot", "Berry", "Morsel", "Steak", "Cooked Morsel", "Cooked Steak" }

-- ESP Helper Functions (moved here to avoid nil value errors)
local function createBillboardLabel(text, color)
    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 120, 0, 30)
    billboard.AlwaysOnTop = true
    billboard.StudsOffset = Vector3.new(0, 3, 0)

    local nameLabel = Instance.new("TextLabel", billboard)
    nameLabel.Size = UDim2.new(1, 0, 1, 0)
    nameLabel.Position = UDim2.new(0, 0, 0, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = text
    nameLabel.TextColor3 = color or Color3.new(1, 1, 1)
    nameLabel.TextStrokeTransparency = 0
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextScaled = true

    return billboard, nameLabel
end

local function startESPThread(name, func)
    if _G.PulseESP.activeThreads[name] then
        task.cancel(_G.PulseESP.activeThreads[name])
    end
    _G.PulseESP.activeThreads[name] = task.spawn(func)
end

local function clearESPHighlights(highlightTable)
    for obj, data in pairs(highlightTable) do
        pcall(function()
            if data.highlight then data.highlight:Destroy() end
            if data.billboard then data.billboard:Destroy() end
        end)
    end
    table.clear(highlightTable)
end

local function updatePlayerESP()
    if not _G.PulseESP.playerESPActive then return end

    if not _G.PulseESP.ESPFolder then
        _G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"
    end

    local localPlayer = game.Players.LocalPlayer
    if not localPlayer.Character or not localPlayer.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = localPlayer.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentPlayers = {}

    for _, player in ipairs(game.Players:GetPlayers()) do
        if player ~= localPlayer and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local distance = (player.Character.HumanoidRootPart.Position - playerPos).Magnitude

            if distance <= maxDistance then
                currentPlayers[player] = true

                if not _G.PulseESP.playerHighlights[player] then
                    local highlight = Instance.new("Highlight")
                    highlight.Adornee = player.Character
                    highlight.FillColor = Color3.fromRGB(255, 100, 100)
                    highlight.OutlineColor = Color3.fromRGB(255, 100, 100)
                    highlight.FillTransparency = 0.5
                    highlight.OutlineTransparency = 0
                    if _G.PulseESP.ESPFolder then
                        highlight.Parent = _G.PulseESP.ESPFolder
                    else
                        highlight.Parent = workspace
                    end

                    local billboard, nameLabel = createBillboardLabel(player.Name, Color3.fromRGB(255, 100, 100))
                    billboard.Adornee = player.Character.HumanoidRootPart
                    if _G.PulseESP.ESPFolder then
                        billboard.Parent = _G.PulseESP.ESPFolder
                    else
                        billboard.Parent = workspace
                    end

                    _G.PulseESP.playerHighlights[player] = {
                        highlight = highlight,
                        billboard = billboard,
                        nameLabel = nameLabel
                    }
                end
            end
        end
    end

    for player, data in pairs(_G.PulseESP.playerHighlights) do
        if not currentPlayers[player] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.playerHighlights[player] = nil
        end
    end
end

-- Specialized ESP Helper Function
local function updateSpecializedESP(itemArray, selectedItem, highlightTable, folder, color, espName)
    if not _G.PulseESP[espName .. "Active"] then return end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentItems = {}

    local workspaceItems = workspace:FindFirstChild("Items")
    if not workspaceItems then return end

    pcall(function()
        for _, obj in ipairs(workspaceItems:GetChildren()) do
            if obj:IsA("Model") and obj.Name and obj.Name ~= "" and obj.PrimaryPart then
                local objName = tostring(obj.Name)

                -- Check if item is in the category array
                local isInCategory = false
                for _, categoryItem in ipairs(itemArray) do
                    if objName == categoryItem then
                        isInCategory = true
                        break
                    end
                end

                if isInCategory and (selectedItem == "ALL" or objName == selectedItem) then
                    local distance = (obj.PrimaryPart.Position - playerPos).Magnitude

                    if distance <= maxDistance then
                        currentItems[obj] = true

                        if not highlightTable[obj] then
                            local highlight = Instance.new("Highlight")
                            highlight.Adornee = obj
                            highlight.FillColor = color
                            highlight.OutlineColor = color
                            highlight.FillTransparency = 0.6
                            highlight.OutlineTransparency = 0
                            if folder then
                                highlight.Parent = folder
                            else
                                highlight.Parent = workspace
                            end

                            local billboard, nameLabel = createBillboardLabel(objName, color)
                            billboard.Adornee = obj.PrimaryPart
                            if folder then
                                billboard.Parent = folder
                            else
                                billboard.Parent = workspace
                            end

                            highlightTable[obj] = {
                                highlight = highlight,
                                billboard = billboard,
                                nameLabel = nameLabel
                            }
                        end
                    end
                end
            end
        end
    end)

    for obj, data in pairs(highlightTable) do
        if not currentItems[obj] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            highlightTable[obj] = nil
        end
    end
end

-- Individual ESP Update Functions
local function updateGunsArmorESP()
    if not _G.PulseESP.GunsArmorESPFolder then
        _G.PulseESP.GunsArmorESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.GunsArmorESPFolder.Name = "GunsArmorESP"
    end
    updateSpecializedESP(GUNS_ARMOR_ITEMS, _G.PulseESP.selectedGunsArmorESP, _G.PulseESP.gunsArmorHighlights, _G.PulseESP.GunsArmorESPFolder, Color3.fromRGB(255, 0, 0), "gunsArmorESP")
end

local function updateFuelESP()
    if not _G.PulseESP.FuelESPFolder then
        _G.PulseESP.FuelESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.FuelESPFolder.Name = "FuelESP"
    end
    updateSpecializedESP(FUEL_ITEMS, _G.PulseESP.selectedFuelESP, _G.PulseESP.fuelHighlights, _G.PulseESP.FuelESPFolder, Color3.fromRGB(255, 165, 0), "fuelESP")
end

local function updateFoodESP()
    if not _G.PulseESP.FoodESPFolder then
        _G.PulseESP.FoodESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.FoodESPFolder.Name = "FoodESP"
    end
    updateSpecializedESP(FOOD_ITEMS, _G.PulseESP.selectedFoodESP, _G.PulseESP.foodHighlights, _G.PulseESP.FoodESPFolder, Color3.fromRGB(0, 255, 0), "foodESP")
end

local function updateCraftingESP()
    if not _G.PulseESP.CraftingESPFolder then
        _G.PulseESP.CraftingESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.CraftingESPFolder.Name = "CraftingESP"
    end
    updateSpecializedESP(CRAFTING_ITEMS, _G.PulseESP.selectedCraftingESP, _G.PulseESP.craftingHighlights, _G.PulseESP.CraftingESPFolder, Color3.fromRGB(0, 100, 255), "craftingESP")
end

local function updateMonsterESP()
    if not _G.PulseESP.monsterESPActive then return end

    if not _G.PulseESP.ESPFolder then
        _G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"
    end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentMonsters = {}

    local charactersFolder = workspace:FindFirstChild("Characters")
    if not charactersFolder then return end

    pcall(function()
        for _, obj in ipairs(charactersFolder:GetChildren()) do
            if obj:IsA("Model") and obj.Name and obj.Name ~= "" and obj.PrimaryPart then
                local objName = tostring(obj.Name)
                local distance = (obj.PrimaryPart.Position - playerPos).Magnitude

                if distance <= maxDistance then
                    currentMonsters[obj] = true

                    if not _G.PulseESP.monsterHighlights[obj] then
                        local highlight = Instance.new("Highlight")
                        highlight.Adornee = obj
                        highlight.FillColor = Color3.fromRGB(255, 0, 255)
                        highlight.OutlineColor = Color3.fromRGB(255, 0, 255)
                        highlight.FillTransparency = 0.6
                        highlight.OutlineTransparency = 0
                        if _G.PulseESP.ESPFolder then
                            highlight.Parent = _G.PulseESP.ESPFolder
                        else
                            highlight.Parent = workspace
                        end

                        local billboard, nameLabel = createBillboardLabel(objName, Color3.fromRGB(255, 0, 255))
                        billboard.Adornee = obj.PrimaryPart
                        if _G.PulseESP.ESPFolder then
                            billboard.Parent = _G.PulseESP.ESPFolder
                        else
                            billboard.Parent = workspace
                        end

                        _G.PulseESP.monsterHighlights[obj] = {
                            highlight = highlight,
                            billboard = billboard,
                            nameLabel = nameLabel
                        }
                    end
                end
            end
        end
    end)

    for obj, data in pairs(_G.PulseESP.monsterHighlights) do
        if not currentMonsters[obj] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.monsterHighlights[obj] = nil
        end
    end
end

-- Helper functions for scanning groups
local function scanAvailableCharacterGroups()
    local groups = {}
    local uniqueNames = {}

    local charactersFolder = workspace:FindFirstChild("Characters")
    if not charactersFolder then return {"ALL"} end

    pcall(function()
        for _, group in ipairs(charactersFolder:GetChildren()) do
            if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                local groupName = tostring(group.Name)
                if not uniqueNames[groupName] then
                    uniqueNames[groupName] = true
                    table.insert(groups, groupName)
                end
            end
        end
    end)

    pcall(function()
        table.sort(groups, function(a, b)
            return tostring(a) < tostring(b)
        end)
    end)

    local result = {"ALL"}
    for i = 1, math.min(#groups, 25) do
        table.insert(result, groups[i])
    end

    return result
end

local function scanAvailableLandmarkGroups()
    local groups = {}
    local uniqueNames = {}

    local mapFolder = workspace:FindFirstChild("Map")
    if not mapFolder then return {} end

    local landmarksFolder = mapFolder:FindFirstChild("Landmarks")
    if not landmarksFolder then return {} end

    pcall(function()
        for _, group in ipairs(landmarksFolder:GetChildren()) do
            if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                local groupName = tostring(group.Name)
                if not uniqueNames[groupName] then
                    uniqueNames[groupName] = true
                    table.insert(groups, groupName)
                end
            end
        end
    end)

    pcall(function()
        table.sort(groups, function(a, b)
            return tostring(a) < tostring(b)
        end)
    end)

    local result = {}
    for i = 1, math.min(#groups, 30) do
        table.insert(result, groups[i])
    end

    return result
end

local function updateCharacterESP()
    if not _G.PulseESP.characterESPActive then return end

    if not _G.PulseESP.ESPFolder then
        _G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"
    end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentCharacters = {}

    local charactersFolder = workspace:FindFirstChild("Characters")
    if not charactersFolder then return end

    pcall(function()
        for _, group in ipairs(charactersFolder:GetChildren()) do
            if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                local groupName = tostring(group.Name)

                local shouldHighlight = false
                if _G.PulseESP.selectedCharacterGroup == "ALL" or groupName == _G.PulseESP.selectedCharacterGroup then
                    shouldHighlight = true
                end

                if shouldHighlight then
                    for _, character in ipairs(group:GetChildren()) do
                        if character:IsA("Model") and character.PrimaryPart then
                            local distance = (character.PrimaryPart.Position - playerPos).Magnitude

                            if distance <= maxDistance then
                                currentCharacters[character] = true

                                if not _G.PulseESP.characterHighlights[character] then
                                    local highlight = Instance.new("Highlight")
                                    highlight.Adornee = character
                                    highlight.FillColor = Color3.fromRGB(255, 255, 0)
                                    highlight.OutlineColor = Color3.fromRGB(255, 255, 0)
                                    highlight.FillTransparency = 0.6
                                    highlight.OutlineTransparency = 0
                                    if _G.PulseESP.ESPFolder then
                                        highlight.Parent = _G.PulseESP.ESPFolder
                                    else
                                        highlight.Parent = workspace
                                    end

                                    local billboard, nameLabel = createBillboardLabel(character.Name, Color3.fromRGB(255, 255, 0))
                                    billboard.Adornee = character.PrimaryPart
                                    if _G.PulseESP.ESPFolder then
                                        billboard.Parent = _G.PulseESP.ESPFolder
                                    else
                                        billboard.Parent = workspace
                                    end

                                    _G.PulseESP.characterHighlights[character] = {
                                        highlight = highlight,
                                        billboard = billboard,
                                        nameLabel = nameLabel
                                    }
                                end
                            end
                        end
                    end
                end
            end
        end
    end)

    for character, data in pairs(_G.PulseESP.characterHighlights) do
        if not currentCharacters[character] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.characterHighlights[character] = nil
        end
    end
end

local function updateMapESP()
    if not _G.PulseESP.mapESPActive then return end

    if not _G.PulseESP.ESPFolder then
        _G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"
    end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentLandmarks = {}

    local mapFolder = workspace:FindFirstChild("Map")
    if not mapFolder then return end

    local landmarksFolder = mapFolder:FindFirstChild("Landmarks")
    if not landmarksFolder then return end

    pcall(function()
        for _, group in ipairs(landmarksFolder:GetChildren()) do
            if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                local groupName = tostring(group.Name)

                local shouldHighlight = false
                for _, selectedGroup in ipairs(_G.PulseESP.selectedLandmarkGroups) do
                    if groupName == selectedGroup then
                        shouldHighlight = true
                        break
                    end
                end

                if shouldHighlight then
                    for _, landmark in ipairs(group:GetChildren()) do
                        if landmark:IsA("Model") and landmark.PrimaryPart then
                            local distance = (landmark.PrimaryPart.Position - playerPos).Magnitude

                            if distance <= maxDistance then
                                currentLandmarks[landmark] = true

                                if not _G.PulseESP.landmarkHighlights[landmark] then
                                    local highlight = Instance.new("Highlight")
                                    highlight.Adornee = landmark
                                    highlight.FillColor = Color3.fromRGB(255, 255, 0)
                                    highlight.OutlineColor = Color3.fromRGB(255, 255, 0)
                                    highlight.FillTransparency = 0.6
                                    highlight.OutlineTransparency = 0
                                    if _G.PulseESP.ESPFolder then
                                        highlight.Parent = _G.PulseESP.ESPFolder
                                    else
                                        highlight.Parent = workspace
                                    end

                                    local billboard, nameLabel = createBillboardLabel(landmark.Name, Color3.fromRGB(255, 255, 0))
                                    billboard.Adornee = landmark.PrimaryPart
                                    if _G.PulseESP.ESPFolder then
                                        billboard.Parent = _G.PulseESP.ESPFolder
                                    else
                                        billboard.Parent = workspace
                                    end

                                    _G.PulseESP.landmarkHighlights[landmark] = {
                                        highlight = highlight,
                                        billboard = billboard,
                                        nameLabel = nameLabel
                                    }
                                end
                            end
                        end
                    end
                end
            end
        end
    end)

    for landmark, data in pairs(_G.PulseESP.landmarkHighlights) do
        if not currentLandmarks[landmark] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.landmarkHighlights[landmark] = nil
        end
    end
end

local function startCombatThread(name, func)
    if _G.PulseCombat.activeThreads[name] then
        task.cancel(_G.PulseCombat.activeThreads[name])
    end
    _G.PulseCombat.activeThreads[name] = task.spawn(func)
end

local function startSurvivalThread(name, func)
    if _G.PulseSurvival.activeThreads[name] then
        task.cancel(_G.PulseSurvival.activeThreads[name])
    end
    _G.PulseSurvival.activeThreads[name] = task.spawn(func)
end

local function startCameraThread(func)
    if _G.PulseCamera.activeThread then
        task.cancel(_G.PulseCamera.activeThread)
    end
    _G.PulseCamera.activeThread = task.spawn(func)
end

local function updateCameraZoom()
    if not _G.PulseCamera.zoomEnabled then return end
    
    local camera = workspace.CurrentCamera
    if not camera then return end
    
    local player = game.Players.LocalPlayer
    if not player then return end
    
    camera.CameraSubject = player.Character and player.Character:FindFirstChild("Humanoid")
    player.CameraMinZoomDistance = _G.PulseCamera.minZoom
    player.CameraMaxZoomDistance = _G.PulseCamera.maxZoom
end

local function startProximityThread(func)
    if _G.PulseProximity.activeThread then
        task.cancel(_G.PulseProximity.activeThread)
    end
    _G.PulseProximity.activeThread = task.spawn(func)
end

local function scanForProximityPrompts(parent)
    local prompts = {}
    
    pcall(function()
        for _, obj in ipairs(parent:GetDescendants()) do
            if obj:IsA("ProximityPrompt") then
                table.insert(prompts, obj)
            end
        end
    end)
    
    return prompts
end

local function modifyProximityPrompt(prompt)
    if not prompt or not prompt.Parent then return end
    
    pcall(function()
        if not _G.PulseProximity.originalValues[prompt] then
            _G.PulseProximity.originalValues[prompt] = prompt.HoldDuration
        end
        
        prompt.HoldDuration = _G.PulseProximity.holdDuration
        _G.PulseProximity.modifiedPrompts[prompt] = true
    end)
end

local function restoreProximityPrompt(prompt)
    if not prompt or not prompt.Parent then return end
    
    pcall(function()
        if _G.PulseProximity.originalValues[prompt] then
            prompt.HoldDuration = _G.PulseProximity.originalValues[prompt]
            _G.PulseProximity.originalValues[prompt] = nil
        end
        _G.PulseProximity.modifiedPrompts[prompt] = nil
    end)
end

local function updateInstantProximityPrompts()
    if not _G.PulseProximity.instantPromptsActive then return end
    
    local allPrompts = scanForProximityPrompts(workspace)
    local currentPrompts = {}
    
    for _, prompt in ipairs(allPrompts) do
        currentPrompts[prompt] = true
        
        if not _G.PulseProximity.modifiedPrompts[prompt] then
            modifyProximityPrompt(prompt)
        end
    end
    
    for prompt, _ in pairs(_G.PulseProximity.modifiedPrompts) do
        if not currentPrompts[prompt] then
            _G.PulseProximity.modifiedPrompts[prompt] = nil
            if _G.PulseProximity.originalValues[prompt] then
                _G.PulseProximity.originalValues[prompt] = nil
            end
        end
    end
end

local function updateAutoEat()
    if not _G.PulseSurvival.autoEatActive then return end
    
    local player = game.Players.LocalPlayer
    if not player.PlayerGui then return end
    
    local success, hungerBar = pcall(function()
        return player.PlayerGui:FindFirstChild("Interface")
            and player.PlayerGui.Interface:FindFirstChild("StatBars")
            and player.PlayerGui.Interface.StatBars:FindFirstChild("HungerBar")
            and player.PlayerGui.Interface.StatBars.HungerBar:FindFirstChild("Bar")
    end)
    
    if not success or not hungerBar then return end
    
    local hungerScale = hungerBar.Size.X.Scale
    if hungerScale < _G.PulseSurvival.hungerThreshold then
        local workspaceItems = workspace:FindFirstChild("Items")
        if not workspaceItems then return end
        
        local foodPriority = {"Cooked Steak", "Cooked Morsel", "Steak", "Carrot", "Berry", "Morsel"}
        
        for _, foodName in ipairs(foodPriority) do
            if not _G.PulseSurvival.autoEatActive then break end
            
            for _, item in ipairs(workspaceItems:GetChildren()) do
                if item:IsA("Model") and item.Name == foodName and item.PrimaryPart then
                    pcall(function()
                        local replicatedStorage = game:GetService("ReplicatedStorage")
                        if replicatedStorage:FindFirstChild("RemoteEvents") and replicatedStorage.RemoteEvents:FindFirstChild("RequestConsumeItem") then
                            replicatedStorage.RemoteEvents.RequestConsumeItem:InvokeServer(item)
                        end
                    end)
                    task.wait(0.3)
                    return
                end
            end
        end
    end
end

local function updateTeleportToFire()
    if not _G.PulseSurvival.teleportToFireActive then return end
    
    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local currentState = workspace:GetAttribute("State")
    if currentState == "Night" and _G.PulseSurvival.lastState ~= "Night" then
        local humanoidRootPart = player.Character.HumanoidRootPart
        
        local campfire = nil
        if workspace:FindFirstChild("Map") and workspace.Map:FindFirstChild("Campground") then
            campfire = workspace.Map.Campground:FindFirstChild("MainFire")
        end
        
        if campfire and campfire.PrimaryPart then
            pcall(function()
                humanoidRootPart.CFrame = CFrame.new(campfire.PrimaryPart.Position + Vector3.new(0, 10, 0))
            end)
        end
    end
    
    _G.PulseSurvival.lastState = currentState
end

local function updateKillAura()
    if not _G.PulseCombat.killAuraActive then return end
    
    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseCombat.killDistance
    
    local success, toolName = pcall(function()
        local toolHandle = player.Character:FindFirstChild("ToolHandle")
        if toolHandle and toolHandle:FindFirstChild("OriginalItem") then
            local toolNameAttr = toolHandle:GetAttribute("ToolName")
            if toolNameAttr == "GenericAxe" or toolNameAttr == "GenericSword" then
                return toolHandle.OriginalItem.Value
            end
        end
        return nil
    end)
    
    if success and toolName then
        local charactersFolder = workspace:FindFirstChild("Characters")
        if not charactersFolder then return end
        
        local characters = charactersFolder:GetChildren()
        for _, model in ipairs(characters) do
            if not _G.PulseCombat.killAuraActive then break end
            
            if model:IsA("Model") and model.PrimaryPart then
                local distance = (model.PrimaryPart.Position - playerPos).Magnitude
                
                if distance <= maxDistance then
                    pcall(function()
                        local replicatedStorage = game:GetService("ReplicatedStorage")
                        if replicatedStorage:FindFirstChild("RemoteEvents") and replicatedStorage.RemoteEvents:FindFirstChild("ToolDamageObject") then
                            replicatedStorage.RemoteEvents.ToolDamageObject:InvokeServer(
                                model,
                                toolName,
                                "1_5221910054",
                                model.PrimaryPart.CFrame
                            )
                        end
                    end)
                end
            end
        end
    end
end

local function createExtendedHitbox(character)
    if not character or not character:FindFirstChild("HumanoidRootPart") then return end

    local humanoidRootPart = character.HumanoidRootPart
    local extendedPart = Instance.new("Part")
    extendedPart.Name = "ExtendedHitbox"
    extendedPart.Size = Vector3.new(_G.PulseCombat.hitboxSize, _G.PulseCombat.hitboxSize, _G.PulseCombat.hitboxSize)
    extendedPart.Shape = Enum.PartType.Block
    extendedPart.Material = Enum.Material.ForceField
    extendedPart.BrickColor = BrickColor.new("Really red")
    extendedPart.Anchored = true
    extendedPart.CanCollide = false
    extendedPart.Transparency = 0.8
    extendedPart.CFrame = humanoidRootPart.CFrame
    extendedPart.Parent = character

    local weld = Instance.new("WeldConstraint")
    weld.Part0 = humanoidRootPart
    weld.Part1 = extendedPart
    weld.Parent = character

    return extendedPart
end

local function removeExtendedHitbox(character)
    if character and character:FindFirstChild("ExtendedHitbox") then
        character.ExtendedHitbox:Destroy()
    end
end

local function updateHitboxExtender()
    if not _G.PulseCombat.hitboxExtenderActive then return end

    local charactersFolder = workspace:FindFirstChild("Characters")
    if not charactersFolder then return end

    local currentCharacters = {}
    pcall(function()
        for _, character in ipairs(charactersFolder:GetChildren()) do
            if character:IsA("Model") and character:FindFirstChild("HumanoidRootPart") then
                currentCharacters[character] = true

                if not _G.PulseCombat.extendedHitboxes[character] then
                    local extendedHitbox = createExtendedHitbox(character)
                    if extendedHitbox then
                        _G.PulseCombat.extendedHitboxes[character] = extendedHitbox
                    end
                else
                    local existingHitbox = _G.PulseCombat.extendedHitboxes[character]
                    if existingHitbox and existingHitbox.Parent then
                        existingHitbox.Size = Vector3.new(_G.PulseCombat.hitboxSize, _G.PulseCombat.hitboxSize, _G.PulseCombat.hitboxSize)
                        existingHitbox.BrickColor = BrickColor.new("Really red")
                    end
                end
            end
        end
    end)

    for character, hitbox in pairs(_G.PulseCombat.extendedHitboxes) do
        if not currentCharacters[character] and character and character.Parent == charactersFolder then
            pcall(function()
                if hitbox and hitbox.Parent then
                    hitbox:Destroy()
                end
            end)
            _G.PulseCombat.extendedHitboxes[character] = nil
        end
    end
end

local Window = WindUI:CreateWindow({
    Title = "PulseHub",
    Icon = "skull",
    Author = "PulseHub Team",
    Folder = "PulseHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    SideBarWidth = 200,
    ScrollBarEnabled = false,
    HideSearchBar = false,
    User = {
        Enabled = true,
        Anonymous = false,
        Callback = function()
        end,
    },
})

Window:SetToggleKey(Enum.KeyCode.Insert)

-- Show update log popup for first-time users (after window is created)
if not hasSeenUpdateLog() then
    -- Small delay to ensure window is fully loaded
    task.wait(0.5)
    showUpdateLogPopup()
else
    print("[PulseHub] Welcome back! Running version " .. SCRIPT_VERSION)
end

local MainTab = Window:Tab({
    Title = "Main",
    Icon = "home",
    Locked = false,
})

local PlayerTab = Window:Tab({
    Title = "Player",
    Icon = "user",
    Locked = false,
})

local ESPTab = Window:Tab({
    Title = "ESP",
    Icon = "eye",
    Locked = false,
})

local ItemsTab = Window:Tab({
    Title = "Items",
    Icon = "package",
    Locked = false,
})

local UITab = Window:Tab({
    Title = "UI",
    Icon = "monitor",
    Locked = false,
})

-- PulseHub Information Section
local pulseHubSection = UITab:Section({
    Title = "PulseHub Information",
    TextXAlignment = "Left",
    TextSize = 17,
})

local showUpdateLogButton = UITab:Button({
    Title = "Show Update Log",
    Desc = "View the latest features and changes",
    Callback = function()
        showUpdateLogPopup()
    end
})

local versionInfoButton = UITab:Button({
    Title = "Version Info",
    Desc = "Current version: " .. SCRIPT_VERSION,
    Callback = function()
        -- Check if stronghold system is loaded
        local autoRefreshStatus = "⏳ Loading..."
        if _G.strongholdAutoRefreshActive ~= nil then
            autoRefreshStatus = _G.strongholdAutoRefreshActive and "✅ Active" or "❌ Disabled"
        end

        WindUI:Popup({
            Title = "PulseHub Version Information",
            Icon = "info",
            Content = [[🔧 Current Version: ]] .. SCRIPT_VERSION .. [[

📊 System Status:
• Script Status: ✅ Running
• Auto-Refresh: ]] .. autoRefreshStatus .. [[
• ESP Systems: ✅ Loaded
• Teleport System: ✅ Ready

📁 Data Storage:
• Folder: PulseHub/
• Settings: Saved locally
• Update Log: Tracked

🎮 Performance:
• Memory Usage: Optimized
• Background Tasks: Minimal
• Game Impact: Low

Thank you for using PulseHub v]] .. SCRIPT_VERSION .. [[!]],
            Buttons = {
                {
                    Title = "Close",
                    Callback = function() end,
                    Variant = "Primary"
                }
            }
        })
    end
})

local resetUpdateLogButton = UITab:Button({
    Title = "Reset Update Log",
    Desc = "Show update log again on next startup",
    Callback = function()
        pcall(function()
            if isfile("PulseHub/" .. UPDATE_LOG_KEY .. ".txt") then
                delfile("PulseHub/" .. UPDATE_LOG_KEY .. ".txt")
            end
        end)
        WindUI:Popup({
            Title = "Update Log Reset",
            Icon = "check",
            Content = "The update log will be shown again the next time you run PulseHub v" .. SCRIPT_VERSION .. ".",
            Buttons = {
                {
                    Title = "OK",
                    Callback = function() end,
                    Variant = "Primary"
                }
            }
        })
    end
})

local TeleportTab = Window:Tab({
    Title = "Teleport",
    Icon = "map-pin",
    Locked = false,
})

local teleportSection = TeleportTab:Section({
    Title = "Locations",
    TextXAlignment = "Left",
    TextSize = 17,
})

local function scanLandmarkLocations()
    local locations = {}
    local mapFolder = workspace:FindFirstChild("Map")
    if not mapFolder then return {"Main Campfire"} end
    
    local landmarksFolder = mapFolder:FindFirstChild("Landmarks")
    if not landmarksFolder then return {"Main Campfire"} end
    
    -- Add Main Campfire as first location
    locations["Main Campfire"] = workspace.Map.Campground.MainFire
    
    -- Scan all landmark groups and their models
    for _, group in ipairs(landmarksFolder:GetChildren()) do
        if group:IsA("Folder") or group:IsA("Model") then
            for _, landmark in ipairs(group:GetChildren()) do
                if landmark:IsA("Model") and landmark.PrimaryPart then
                    local locationName = group.Name .. " - " .. landmark.Name
                    locations[locationName] = landmark
                end
            end
        end
    end
    
    return locations
end

local teleportLocations = scanLandmarkLocations()

local locationNames = {"Main Campfire"}
for name, _ in pairs(teleportLocations) do
    if name ~= "Main Campfire" then
        table.insert(locationNames, name)
    end
end
table.sort(locationNames)

local function teleportToLocation(location)
    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end
    
    if location and location.PrimaryPart then
        player.Character.HumanoidRootPart.CFrame = CFrame.new(location.PrimaryPart.Position + Vector3.new(0, 5, 0))
    end
end

local locationDropdown = TeleportTab:Dropdown({
    Title = "Locations",
    Desc = "Select location to teleport",
    Values = locationNames,
    Default = "Main Campfire",
    Value = "Main Campfire",
    Callback = function(selected)
        local targetLocation = teleportLocations[selected]
        if targetLocation then
            teleportToLocation(targetLocation)
        end
    end
})

local teleportButton = TeleportTab:Button({
    Title = "Teleport",
    Desc = "Teleport to selected location",
    Callback = function()
        local selected = locationDropdown.Value
        local targetLocation = teleportLocations[selected]
        if targetLocation then
            teleportToLocation(targetLocation)
        end
    end
})

local refreshLocationsButton = TeleportTab:Button({
    Title = "Refresh",
    Desc = "Update location list",
    Callback = function()
        teleportLocations = scanLandmarkLocations()
        local newLocationNames = {"Main Campfire"}
        for name, _ in pairs(teleportLocations) do
            if name ~= "Main Campfire" then
                table.insert(newLocationNames, name)
            end
        end
        table.sort(newLocationNames)
        
        if locationDropdown and locationDropdown.Refresh then
            locationDropdown:Refresh(newLocationNames)
        elseif locationDropdown and locationDropdown.Update then
            locationDropdown:Update(newLocationNames)
        end
    end
})

-- Stronghold Section
local strongholdSection = TeleportTab:Section({
    Title = "Stronghold",
    TextXAlignment = "Left",
    TextSize = 17,
})

local function scanStrongholdLocations()
    local locations = {}
    local mapFolder = workspace:FindFirstChild("Map")
    if not mapFolder then
        print("[PulseHub] Map folder not found")
        return {}
    end

    local landmarksFolder = mapFolder:FindFirstChild("Landmarks")
    if not landmarksFolder then
        print("[PulseHub] Landmarks folder not found")
        return {}
    end

    local strongholdFolder = landmarksFolder:FindFirstChild("Stronghold")
    if not strongholdFolder then
        print("[PulseHub] Stronghold folder not found in workspace.Map.Landmarks.Stronghold")
        return {}
    end

    print("[PulseHub] Found stronghold folder, scanning for locations...")

    for _, landmark in ipairs(strongholdFolder:GetChildren()) do
        if landmark:IsA("Model") and landmark.PrimaryPart then
            locations[landmark.Name] = landmark
            print("[PulseHub] Found stronghold location: " .. landmark.Name)
        end
    end

    print("[PulseHub] Total stronghold locations found: " .. tostring(#locations))
    return locations
end

local strongholdLocations = scanStrongholdLocations()
local strongholdNames = {}
for name, _ in pairs(strongholdLocations) do
    table.insert(strongholdNames, name)
end
table.sort(strongholdNames)

-- Add fallback if no strongholds found
if #strongholdNames == 0 then
    strongholdNames = {"No Strongholds Found"}
    print("[PulseHub] No stronghold locations found, adding fallback option")
end

-- Auto-refresh system for strongholds
local lastStrongholdCount = #strongholdNames == 1 and strongholdNames[1] == "No Strongholds Found" and 0 or #strongholdNames
_G.strongholdAutoRefreshActive = true
local strongholdRefreshThread = nil

local function createStrongholdNotification(title, message)
    -- Try different notification methods based on what's available
    pcall(function()
        if Window and Window.Notify then
            Window:Notify({
                Title = title,
                Content = message,
                Duration = 5
            })
        elseif Window and Window.Notification then
            Window:Notification({
                Title = title,
                Text = message,
                Time = 5
            })
        else
            print("[PulseHub Notification] " .. title .. ": " .. message)
        end
    end)
end

local function autoRefreshStrongholds()
    local scanCount = 0
    while _G.strongholdAutoRefreshActive do
        task.wait(5) -- Wait 5 seconds between checks

        if not _G.strongholdAutoRefreshActive then break end

        scanCount = scanCount + 1
        print("[PulseHub Auto-Refresh] Scan #" .. scanCount .. " - Checking for strongholds...")

        local newStrongholdLocations = scanStrongholdLocations()
        local newStrongholdNames = {}
        for name, _ in pairs(newStrongholdLocations) do
            table.insert(newStrongholdNames, name)
        end
        table.sort(newStrongholdNames)

        local currentCount = #newStrongholdNames

        -- Check if new strongholds were found
        if currentCount > lastStrongholdCount then
            local newStrongholds = {}

            -- Find which strongholds are new
            for _, newName in ipairs(newStrongholdNames) do
                local isNew = true
                for oldName, _ in pairs(strongholdLocations) do
                    if oldName == newName then
                        isNew = false
                        break
                    end
                end
                if isNew then
                    table.insert(newStrongholds, newName)
                end
            end

            if #newStrongholds > 0 then
                local message = "Found " .. #newStrongholds .. " new stronghold(s): " .. table.concat(newStrongholds, ", ")
                createStrongholdNotification("Strongholds Detected!", message)
                print("[PulseHub Auto-Refresh] " .. message)

                -- Update the global variables
                strongholdLocations = newStrongholdLocations
                strongholdNames = newStrongholdNames
                lastStrongholdCount = currentCount

                -- Update the dropdown
                if strongholdDropdown then
                    if strongholdDropdown.Refresh then
                        strongholdDropdown:Refresh(newStrongholdNames)
                    elseif strongholdDropdown.Update then
                        strongholdDropdown:Update(newStrongholdNames)
                    elseif strongholdDropdown.SetValues then
                        strongholdDropdown:SetValues(newStrongholdNames)
                    end
                end

                -- Update ESP if active
                if _G.PulseESP.strongholdESPActive then
                    clearESPHighlights(_G.PulseESP.strongholdHighlights)
                    updateStrongholdESP()
                end
            end
        elseif currentCount == 0 and lastStrongholdCount == 0 then
            -- Still no strongholds found, but don't spam notifications
            -- Just update the count check
        end
    end
end

-- Start the auto-refresh thread
strongholdRefreshThread = task.spawn(autoRefreshStrongholds)

-- Initial notification if strongholds were found immediately
if lastStrongholdCount > 0 then
    createStrongholdNotification("Strongholds Ready!", "Found " .. lastStrongholdCount .. " stronghold location(s)")
end

local strongholdDropdown = TeleportTab:Dropdown({
    Title = "Stronghold Locations",
    Desc = "Select stronghold location",
    Values = strongholdNames,
    Default = strongholdNames[1] or "",
    Value = strongholdNames[1] or "",
    Callback = function(selected)
        if selected == "No Strongholds Found" then
            print("[PulseHub] No strongholds available to teleport to")
            return
        end

        local targetLocation = strongholdLocations[selected]
        if targetLocation then
            print("[PulseHub] Teleporting to stronghold: " .. selected)
            teleportToLocation(targetLocation)
        else
            print("[PulseHub] Stronghold location not found: " .. tostring(selected))
        end
    end
})

-- Stronghold ESP System
if not _G.PulseESP then
    _G.PulseESP = {
        activeThreads = {},
        ESPFolder = nil,
        StrongholdESPFolder = nil,
        strongholdHighlights = {},
        strongholdESPActive = false,
        playerHighlights = {},
        landmarkHighlights = {},
        mapESPActive = false,
        renderDistance = 1000,
        selectedLandmarkGroups = {}
    }
end

if not _G.PulseCombat then
    _G.PulseCombat = {
        activeThreads = {},
        killAuraActive = false,
        killDistance = 125,
        hitboxExtenderActive = false,
        hitboxSize = 20
    }
end

if not _G.PulseProximity then
    _G.PulseProximity = {
        originalValues = {},
        active = false
    }
end

if not _G.PulseESP.StrongholdESPFolder then
    _G.PulseESP.StrongholdESPFolder = Instance.new("Folder", workspace)
    _G.PulseESP.StrongholdESPFolder.Name = "StrongholdESP"
end

_G.PulseESP.strongholdHighlights = _G.PulseESP.strongholdHighlights or {}
_G.PulseESP.strongholdESPActive = false

local function updateStrongholdESP()
    if not _G.PulseESP.strongholdESPActive then return end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local currentStrongholds = {}

    local mapFolder = workspace:FindFirstChild("Map")
    if not mapFolder then
        print("[PulseHub ESP] Map folder not found for stronghold ESP")
        return
    end

    local landmarksFolder = mapFolder:FindFirstChild("Landmarks")
    if not landmarksFolder then
        print("[PulseHub ESP] Landmarks folder not found for stronghold ESP")
        return
    end

    local strongholdFolder = landmarksFolder:FindFirstChild("Stronghold")
    if not strongholdFolder then
        print("[PulseHub ESP] Stronghold folder not found at workspace.Map.Landmarks.Stronghold")
        return
    end

    print("[PulseHub ESP] Updating stronghold ESP...")
    local strongholdCount = 0

    for _, landmark in ipairs(strongholdFolder:GetChildren()) do
        if landmark:IsA("Model") and landmark.PrimaryPart then
            strongholdCount = strongholdCount + 1
            local distance = (landmark.PrimaryPart.Position - playerPos).Magnitude
            currentStrongholds[landmark] = true

            if not _G.PulseESP.strongholdHighlights[landmark] then
                local highlight = Instance.new("Highlight")
                highlight.Adornee = landmark
                highlight.FillColor = Color3.fromRGB(255, 165, 0)  -- Orange color for strongholds
                highlight.OutlineColor = Color3.fromRGB(255, 165, 0)
                highlight.FillTransparency = 0.5
                highlight.OutlineTransparency = 0
                highlight.Parent = _G.PulseESP.StrongholdESPFolder

                local billboard, nameLabel = createBillboardLabel(landmark.Name, Color3.fromRGB(255, 165, 0))
                billboard.Adornee = landmark.PrimaryPart
                billboard.Parent = _G.PulseESP.StrongholdESPFolder

                _G.PulseESP.strongholdHighlights[landmark] = {
                    highlight = highlight,
                    billboard = billboard,
                    nameLabel = nameLabel
                }

                print("[PulseHub ESP] Added ESP for stronghold: " .. landmark.Name)
            end
        end
    end

    print("[PulseHub ESP] Found " .. strongholdCount .. " strongholds for ESP")

    for landmark, data in pairs(_G.PulseESP.strongholdHighlights) do
        if not currentStrongholds[landmark] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.strongholdHighlights[landmark] = nil
        end
    end
end

local strongholdESP = TeleportTab:Toggle({
    Title = "Stronghold ESP",
    Desc = "Show stronghold locations",
    Icon = "castle",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.strongholdESPActive = state
        
        if state then
            startESPThread("StrongholdESP", function()
                while _G.PulseESP.strongholdESPActive do
                    pcall(updateStrongholdESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["StrongholdESP"] then
                task.cancel(_G.PulseESP.activeThreads["StrongholdESP"])
                _G.PulseESP.activeThreads["StrongholdESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.strongholdHighlights)
        end
    end
})

-- Auto-refresh toggle
local autoRefreshToggle = TeleportTab:Toggle({
    Title = "Auto-Refresh Strongholds",
    Desc = "Automatically scan for new strongholds every 5 seconds",
    Icon = "refresh-cw",
    Type = "Checkbox",
    Default = true,
    Callback = function(state)
        _G.strongholdAutoRefreshActive = state
        if state then
            if strongholdRefreshThread then
                task.cancel(strongholdRefreshThread)
            end
            strongholdRefreshThread = task.spawn(autoRefreshStrongholds)
            createStrongholdNotification("Auto-Refresh Enabled", "Scanning for strongholds every 5 seconds")
            print("[PulseHub] Stronghold auto-refresh enabled")
        else
            if strongholdRefreshThread then
                task.cancel(strongholdRefreshThread)
                strongholdRefreshThread = nil
            end
            createStrongholdNotification("Auto-Refresh Disabled", "Manual refresh only")
            print("[PulseHub] Stronghold auto-refresh disabled")
        end
    end
})

local refreshStrongholdButton = TeleportTab:Button({
    Title = "Manual Refresh",
    Desc = "Manually update stronghold locations now",
    Callback = function()
        print("[PulseHub] Manual refresh of stronghold locations...")
        local oldCount = lastStrongholdCount

        strongholdLocations = scanStrongholdLocations()
        local newStrongholdNames = {}
        for name, _ in pairs(strongholdLocations) do
            table.insert(newStrongholdNames, name)
        end
        table.sort(newStrongholdNames)

        local currentCount = #newStrongholdNames

        -- Add fallback if no strongholds found
        if currentCount == 0 then
            newStrongholdNames = {"No Strongholds Found"}
            print("[PulseHub] No stronghold locations found after manual refresh")
        else
            print("[PulseHub] Found " .. currentCount .. " stronghold locations after manual refresh")
            lastStrongholdCount = currentCount
        end

        -- Update stronghold names
        strongholdNames = newStrongholdNames

        -- Try different methods to update the dropdown
        if strongholdDropdown then
            if strongholdDropdown.Refresh then
                strongholdDropdown:Refresh(newStrongholdNames)
                print("[PulseHub] Updated dropdown using Refresh method")
            elseif strongholdDropdown.Update then
                strongholdDropdown:Update(newStrongholdNames)
                print("[PulseHub] Updated dropdown using Update method")
            elseif strongholdDropdown.SetValues then
                strongholdDropdown:SetValues(newStrongholdNames)
                print("[PulseHub] Updated dropdown using SetValues method")
            else
                print("[PulseHub] No update method found for dropdown")
            end
        else
            print("[PulseHub] Stronghold dropdown not found")
        end

        if _G.PulseESP.strongholdESPActive then
            clearESPHighlights(_G.PulseESP.strongholdHighlights)
            updateStrongholdESP()
        end

        -- Show notification about the manual refresh
        if currentCount > oldCount then
            createStrongholdNotification("Manual Refresh Complete", "Found " .. (currentCount - oldCount) .. " new stronghold(s)")
        else
            createStrongholdNotification("Manual Refresh Complete", "Found " .. currentCount .. " stronghold(s) total")
        end
    end
})

-- Status display for stronghold system
local strongholdStatusButton = TeleportTab:Button({
    Title = "Stronghold Status",
    Desc = "Show current stronghold detection status",
    Callback = function()
        local currentCount = 0
        for _, _ in pairs(strongholdLocations) do
            currentCount = currentCount + 1
        end

        local statusMessage = "Current Status:\n" ..
                            "• Strongholds Found: " .. currentCount .. "\n" ..
                            "• Auto-Refresh: " .. (_G.strongholdAutoRefreshActive and "Enabled" or "Disabled") .. "\n" ..
                            "• ESP Active: " .. (_G.PulseESP.strongholdESPActive and "Yes" or "No")

        createStrongholdNotification("Stronghold Status", statusMessage)
        print("[PulseHub Status] " .. statusMessage:gsub("\n", " | "))
    end
})

-- Cleanup function for when script is destroyed
local function cleanupStrongholdSystem()
    _G.strongholdAutoRefreshActive = false
    if strongholdRefreshThread then
        task.cancel(strongholdRefreshThread)
        strongholdRefreshThread = nil
    end
    print("[PulseHub] Stronghold auto-refresh system cleaned up")
end

-- Register cleanup (this will run when the script is destroyed)
game:GetService("Players").PlayerRemoving:Connect(function(player)
    if player == game.Players.LocalPlayer then
        cleanupStrongholdSystem()
    end
end)

-- Debug button to test stronghold path
local debugStrongholdButton = TeleportTab:Button({
    Title = "Debug Stronghold Path",
    Desc = "Check if stronghold path exists",
    Callback = function()
        print("[PulseHub Debug] Checking stronghold path...")
        print("[PulseHub Debug] workspace exists: " .. tostring(workspace ~= nil))

        local map = workspace:FindFirstChild("Map")
        print("[PulseHub Debug] workspace.Map exists: " .. tostring(map ~= nil))

        if map then
            local landmarks = map:FindFirstChild("Landmarks")
            print("[PulseHub Debug] workspace.Map.Landmarks exists: " .. tostring(landmarks ~= nil))

            if landmarks then
                local stronghold = landmarks:FindFirstChild("Stronghold")
                print("[PulseHub Debug] workspace.Map.Landmarks.Stronghold exists: " .. tostring(stronghold ~= nil))

                if stronghold then
                    local children = stronghold:GetChildren()
                    print("[PulseHub Debug] Stronghold has " .. #children .. " children")

                    for i, child in ipairs(children) do
                        print("[PulseHub Debug] Child " .. i .. ": " .. child.Name .. " (Type: " .. child.ClassName .. ")")
                        if child:IsA("Model") then
                            print("[PulseHub Debug]   - Has PrimaryPart: " .. tostring(child.PrimaryPart ~= nil))
                        end
                    end
                else
                    print("[PulseHub Debug] Stronghold folder not found!")
                    print("[PulseHub Debug] Available children in Landmarks:")
                    for i, child in ipairs(landmarks:GetChildren()) do
                        print("[PulseHub Debug]   - " .. child.Name)
                    end
                end
            else
                print("[PulseHub Debug] Landmarks folder not found!")
                print("[PulseHub Debug] Available children in Map:")
                for i, child in ipairs(map:GetChildren()) do
                    print("[PulseHub Debug]   - " .. child.Name)
                end
            end
        end
    end
})

local mainSection = MainTab:Section({
    Title = "Visual",
    TextXAlignment = "Left",
    TextSize = 17,
})

local fullBright = MainTab:Toggle({
    Title = "Full Bright",
    Desc = "Makes everything bright",
    Icon = "sun",
    Type = "Checkbox",
    Default = false,
    Callback = function(value)
        local lighting = game:GetService("Lighting")
        local tweenService = game:GetService("TweenService")
        
        local info = TweenInfo.new(
            1.5,
            Enum.EasingStyle.Quint,
            Enum.EasingDirection.InOut,
            0,
            false,
            0
        )
        
        if value then
            local brightness = tweenService:Create(lighting, info, {
                Brightness = 2
            })
            local clockTime = tweenService:Create(lighting, info, {
                ClockTime = 14
            })
            local ambient = tweenService:Create(lighting, info, {
                OutdoorAmbient = Color3.fromRGB(128, 128, 128)
            })
            local fog = tweenService:Create(lighting, info, {
                FogEnd = 100000
            })
            
            brightness:Play()
            clockTime:Play() 
            ambient:Play()
            fog:Play()
            
            lighting.GlobalShadows = false
        else
            local brightness = tweenService:Create(lighting, info, {
                Brightness = 1
            })
            local clockTime = tweenService:Create(lighting, info, {
                ClockTime = 12
            })
            local ambient = tweenService:Create(lighting, info, {
                OutdoorAmbient = Color3.fromRGB(70, 70, 70)
            })
            local fog = tweenService:Create(lighting, info, {
                FogEnd = 100000
            })
            
            brightness:Play()
            clockTime:Play()
            ambient:Play()
            fog:Play()
            
            lighting.GlobalShadows = true
        end
    end
})

local removeFog = MainTab:Button({
    Title = "Remove Fog",
    Desc = "Delete boundaries",
    Callback = function()
        if workspace:FindFirstChild("Map") and workspace.Map:FindFirstChild("Boundaries") then
            workspace.Map.Boundaries:Destroy()
        end
    end
})

local combatSection = MainTab:Section({
    Title = "Combat",
    TextXAlignment = "Left",
    TextSize = 17,
})

local deleteEntities = MainTab:Button({
    Title = "Delete All Entities",
    Desc = "Remove all entities from the game",
    Callback = function()
        local charactersFolder = workspace:FindFirstChild("Characters")
        if charactersFolder then
            charactersFolder:Destroy()
        end
    end
})

local killAura = MainTab:Toggle({
    Title = "Kill Aura",
    Desc = "Auto attack nearby entities",
    Icon = "sword",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseCombat.killAuraActive = state

        if state then
            startCombatThread("KillAura", function()
                while _G.PulseCombat.killAuraActive do
                    pcall(updateKillAura)
                    task.wait(0.5)
                end
            end)
        else
            if _G.PulseCombat.activeThreads["KillAura"] then
                task.cancel(_G.PulseCombat.activeThreads["KillAura"])
                _G.PulseCombat.activeThreads["KillAura"] = nil
            end
        end
    end
})

local killDistance = MainTab:Slider({
    Title = "Kill Range",
    Desc = "Max distance for kill aura",
    Step = 5,
    Value = {
        Min = 25,
        Max = 200,
        Default = 125,
    },
    Callback = function(val)
        _G.PulseCombat.killDistance = tonumber(val) or 125
    end
})

local hitboxExtender = MainTab:Toggle({
    Title = "Hitbox Extender",
    Desc = "Extend hitboxes for bunnies and wolves",
    Icon = "target",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseCombat.hitboxExtenderActive = state
        updateHitboxExtender()
    end
})

local hitboxSize = MainTab:Slider({
    Title = "Hitbox Size",
    Desc = "Size of hitboxes",
    Step = 2,
    Value = {
        Min = 10,
        Max = 50,
        Default = 20,
    },
    Callback = function(val)
        _G.PulseCombat.hitboxSize = tonumber(val) or 20
        updateHitboxExtender()
    end
})

local survivalSection = MainTab:Section({
    Title = "Survival",
    TextXAlignment = "Left",
    TextSize = 17,
})

local autoEat = MainTab:Toggle({
    Title = "Auto Eat",
    Desc = "Auto consume food when hungry",
    Icon = "utensils",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseSurvival.autoEatActive = state

        if state then
            startSurvivalThread("AutoEat", function()
                while _G.PulseSurvival.autoEatActive do
                    pcall(updateAutoEat)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseSurvival.activeThreads["AutoEat"] then
                task.cancel(_G.PulseSurvival.activeThreads["AutoEat"])
                _G.PulseSurvival.activeThreads["AutoEat"] = nil
            end
        end
    end
})

local hungerThreshold = MainTab:Slider({
    Title = "Hunger Level",
    Desc = "Eat when hunger drops below this",
    Step = 0.05,
    Value = {
        Min = 0.2,
        Max = 0.95,
        Default = 0.7,
    },
    Callback = function(val)
        _G.PulseSurvival.hungerThreshold = tonumber(val) or 0.7
    end
})

local teleportToFire = MainTab:Toggle({
    Title = "Night TP Fire",
    Desc = "Auto TP to campfire at night",
    Icon = "moon",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseSurvival.teleportToFireActive = state
        _G.PulseSurvival.lastState = nil

        if state then
            startSurvivalThread("TeleportToFire", function()
                while _G.PulseSurvival.teleportToFireActive do
                    pcall(updateTeleportToFire)
                    task.wait(1)
                end
            end)
        else
            if _G.PulseSurvival.activeThreads["TeleportToFire"] then
                task.cancel(_G.PulseSurvival.activeThreads["TeleportToFire"])
                _G.PulseSurvival.activeThreads["TeleportToFire"] = nil
            end
        end
    end
})

local interactionSection = MainTab:Section({
    Title = "Interaction",
    TextXAlignment = "Left",
    TextSize = 17,
})

local instantProximityPrompts = MainTab:Toggle({
    Title = "Instant Prompts",
    Desc = "Make all prompts instant",
    Icon = "zap",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseProximity.instantPromptsActive = state

        if state then
            startProximityThread(function()
                while _G.PulseProximity.instantPromptsActive do
                    pcall(updateInstantProximityPrompts)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseProximity.activeThread then
                task.cancel(_G.PulseProximity.activeThread)
                _G.PulseProximity.activeThread = nil
            end
            
            for prompt, _ in pairs(_G.PulseProximity.modifiedPrompts) do
                restoreProximityPrompt(prompt)
            end
            
            _G.PulseProximity.modifiedPrompts = {}
            _G.PulseProximity.originalValues = {}
        end
    end
})

local holdDuration = MainTab:Slider({
    Title = "Hold Time",
    Desc = "Custom hold time for prompts",
    Step = 0.05,
    Value = {
        Min = 0.05,
        Max = 2.0,
        Default = 0.1,
    },
    Callback = function(val)
        _G.PulseProximity.holdDuration = tonumber(val) or 0.1
        
        if _G.PulseProximity.instantPromptsActive then
            for prompt, _ in pairs(_G.PulseProximity.modifiedPrompts) do
                pcall(function()
                    prompt.HoldDuration = _G.PulseProximity.holdDuration
                end)
            end
        end
    end
})

local scanProximityButton = MainTab:Button({
    Title = "Scan Prompts",
    Desc = "Count all proximity prompts",
    Callback = function()
        local allPrompts = scanForProximityPrompts(workspace)
        local modifiedCount = 0
        
        for _, _ in pairs(_G.PulseProximity.modifiedPrompts) do
            modifiedCount = modifiedCount + 1
        end
    end
})

local playerSection = PlayerTab:Section({
    Title = "Player",
    TextXAlignment = "Left",
    TextSize = 17,
})

local walkSpeed = PlayerTab:Slider({
    Title = "Speed",
    Desc = "Movement speed",
    Step = 1,
    Value = {
        Min = 16,
        Max = 200,
        Default = 16,
    },
    Callback = function(val)
        local speed = tonumber(val) or 16
        if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
            game.Players.LocalPlayer.Character.Humanoid.WalkSpeed = speed
        end
    end
})

local jumpPower = PlayerTab:Slider({
    Title = "Jump",
    Desc = "Jump height",
    Step = 1,
    Value = {
        Min = 50,
        Max = 300,
        Default = 50,
    },
    Callback = function(val)
        local power = tonumber(val) or 50
        if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
            game.Players.LocalPlayer.Character.Humanoid.JumpPower = power
        end
    end
})

local noclip = PlayerTab:Toggle({
    Title = "Noclip",
    Desc = "Walk through walls",
    Icon = "ghost",
    Type = "Checkbox",
    Default = false,
    Callback = function(enabled)
        local plr = game.Players.LocalPlayer
        local char = plr.Character
        if char then
            for i, v in pairs(char:GetChildren()) do
                if v:IsA("BasePart") then
                    v.CanCollide = not enabled
                end
            end
        end
    end
})

local infJump = PlayerTab:Toggle({
    Title = "Inf Jump",
    Desc = "Unlimited jumps",
    Icon = "arrow-up",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.infJump = state
        if state then
            _G.infJumpConnection = game:GetService("UserInputService").JumpRequest:Connect(function()
                if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
                    game.Players.LocalPlayer.Character.Humanoid:ChangeState("Jumping")
                end
            end)
        else
            if _G.infJumpConnection then
                _G.infJumpConnection:Disconnect()
                _G.infJumpConnection = nil
            end
        end
    end
})

local flySpeed = PlayerTab:Slider({
    Title = "Fly Speed",
    Desc = "Fly speed control",
    Step = 5,
    Value = {
        Min = 10,
        Max = 200,
        Default = 50,
    },
    Callback = function(val)
        _G.flySpeed = tonumber(val) or 50
    end
})

local fly = PlayerTab:Toggle({
    Title = "Fly",
    Desc = "Fly around",
    Icon = "feather",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        if state then
            sFLY(false)
        else
            if _G.FLYING then
                _G.FLYING = false
            end
            if flyKeyDown then flyKeyDown:Disconnect() end
            if flyKeyUp then flyKeyUp:Disconnect() end
        end
    end
})

local godMode = PlayerTab:Toggle({
    Title = "God Mode",
    Desc = "No damage",
    Icon = "shield",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.godMode = state
    end
})

local cameraSection = PlayerTab:Section({
    Title = "Camera",
    TextXAlignment = "Left",
    TextSize = 17,
})

local cameraZoom = PlayerTab:Toggle({
    Title = "Zoom",
    Desc = "Custom camera zoom",
    Icon = "zoom-in",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseCamera.zoomEnabled = state
        
        if state then
            startCameraThread(function()
                while _G.PulseCamera.zoomEnabled do
                    pcall(updateCameraZoom)
                    task.wait(0.1)
                end
            end)
        else
            if _G.PulseCamera.activeThread then
                task.cancel(_G.PulseCamera.activeThread)
                _G.PulseCamera.activeThread = nil
            end
            
            local player = game.Players.LocalPlayer
            if player then
                player.CameraMinZoomDistance = 0.5
                player.CameraMaxZoomDistance = 400
            end
        end
    end
})

local minZoom = PlayerTab:Slider({
    Title = "Min Zoom",
    Desc = "Min zoom distance",
    Step = 0.1,
    Value = {
        Min = 0.1,
        Max = 50,
        Default = 0.5,
    },
    Callback = function(val)
        _G.PulseCamera.minZoom = tonumber(val) or 0.5
        if _G.PulseCamera.zoomEnabled then
            updateCameraZoom()
        end
    end
})

local maxZoom = PlayerTab:Slider({
    Title = "Max Zoom",
    Desc = "Max zoom distance",
    Step = 10,
    Value = {
        Min = 50,
        Max = 2000,
        Default = 400,
    },
    Callback = function(val)
        _G.PulseCamera.maxZoom = tonumber(val) or 400
        if _G.PulseCamera.zoomEnabled then
            updateCameraZoom()
        end
    end
})

local espSection = ESPTab:Section({
    Title = "ESP System",
    TextXAlignment = "Left",
    TextSize = 17,
})



-- Create ESP Folders
if not _G.PulseESP.ESPFolder then
    _G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
    _G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"
end

if not _G.PulseESP.GunsArmorESPFolder then
    _G.PulseESP.GunsArmorESPFolder = Instance.new("Folder", workspace)
    _G.PulseESP.GunsArmorESPFolder.Name = "GunsArmorESP"
end

if not _G.PulseESP.FuelESPFolder then
    _G.PulseESP.FuelESPFolder = Instance.new("Folder", workspace)
    _G.PulseESP.FuelESPFolder.Name = "FuelESP"
end

if not _G.PulseESP.FoodESPFolder then
    _G.PulseESP.FoodESPFolder = Instance.new("Folder", workspace)
    _G.PulseESP.FoodESPFolder.Name = "FoodESP"
end

if not _G.PulseESP.CraftingESPFolder then
    _G.PulseESP.CraftingESPFolder = Instance.new("Folder", workspace)
    _G.PulseESP.CraftingESPFolder.Name = "CraftingESP"
end

local playerESP = ESPTab:Toggle({
    Title = "Players",
    Desc = "Show players through walls",
    Icon = "users",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.playerESPActive = state

        if state then
            startESPThread("PlayerESP", function()
                while _G.PulseESP.playerESPActive do
                    pcall(updatePlayerESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["PlayerESP"] then
                task.cancel(_G.PulseESP.activeThreads["PlayerESP"])
                _G.PulseESP.activeThreads["PlayerESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.playerHighlights)
        end
    end
})














local function updateMonsterESP()
    if not _G.PulseESP.monsterESPActive then return end
    
    if not _G.PulseESP.ESPFolder then
        _G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"
    end
    
    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentMonsters = {}
    
    local monsterNames = {"Monster", "Zombie", "Enemy", "Boss", "Creature", "Beast"}
    
    for _, folder in ipairs(workspace:GetChildren()) do
        if folder:IsA("Folder") or folder:IsA("Model") then
            for _, obj in ipairs(folder:GetChildren()) do
                if obj:IsA("Model") and obj.PrimaryPart then
                    local objName = obj.Name:lower()
                    local isMonster = false
                    
                    for _, monsterName in ipairs(monsterNames) do
                        if objName:find(monsterName:lower()) then
                            isMonster = true
                            break
                        end
                    end
                    
                    if isMonster then
                        local distance = (obj.PrimaryPart.Position - playerPos).Magnitude
                        
                        if distance <= maxDistance then
                            currentMonsters[obj] = true
                            
                            if not _G.PulseESP.monsterHighlights[obj] then
                                local highlight = Instance.new("Highlight")
                                highlight.Adornee = obj
                                highlight.FillColor = Color3.fromRGB(255, 0, 0)
                                highlight.OutlineColor = Color3.fromRGB(255, 0, 0)
                                highlight.FillTransparency = 0.4
                                highlight.OutlineTransparency = 0
                                if _G.PulseESP.ESPFolder then
                                    highlight.Parent = _G.PulseESP.ESPFolder
                                else
                                    highlight.Parent = workspace
                                end
                                
                                local billboard, nameLabel = createBillboardLabel(obj.Name, Color3.fromRGB(255, 0, 0))
                                billboard.Adornee = obj.PrimaryPart
                                if _G.PulseESP.ESPFolder then
                                    billboard.Parent = _G.PulseESP.ESPFolder
                                else
                                    billboard.Parent = workspace
                                end
                                
                                _G.PulseESP.monsterHighlights[obj] = {
                                    highlight = highlight,
                                    billboard = billboard,
                                    nameLabel = nameLabel
                                }
                            end
                        end
                    end
                end
            end
        end
    end
    
    for obj, data in pairs(_G.PulseESP.monsterHighlights) do
        if not currentMonsters[obj] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.monsterHighlights[obj] = nil
        end
    end
end

local function updateCharacterESP()
    if not _G.PulseESP.characterESPActive then return end

    if not _G.PulseESP.ESPFolder then
        _G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
        _G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"
    end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentCharacters = {}

    local charactersFolder = workspace:FindFirstChild("Characters")
    if not charactersFolder then return end

    pcall(function()
        for _, group in ipairs(charactersFolder:GetChildren()) do
            if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                local groupName = tostring(group.Name)

                -- Check if we should highlight this group based on selection
                if _G.PulseESP.selectedCharacterGroup == "ALL" or groupName == _G.PulseESP.selectedCharacterGroup then
                    for _, character in ipairs(group:GetChildren()) do
                        if character:IsA("Model") and character.PrimaryPart then
                            local distance = (character.PrimaryPart.Position - playerPos).Magnitude

                            if distance <= maxDistance then
                                currentCharacters[character] = true

                                if not _G.PulseESP.characterHighlights[character] then
                                    local highlight = Instance.new("Highlight")
                                    highlight.Adornee = character
                                    highlight.FillColor = Color3.fromRGB(255, 255, 0)
                                    highlight.OutlineColor = Color3.fromRGB(255, 255, 0)
                                    highlight.FillTransparency = 0.5
                                    highlight.OutlineTransparency = 0
                                    if _G.PulseESP.ESPFolder then
                                        highlight.Parent = _G.PulseESP.ESPFolder
                                    else
                                        highlight.Parent = workspace
                                    end

                                    -- Include group name in the label for better identification
                                    local characterLabel = character.Name .. " (" .. groupName .. ")"
                                    local billboard, nameLabel = createBillboardLabel(characterLabel, Color3.fromRGB(255, 255, 0))
                                    billboard.Adornee = character.PrimaryPart
                                    if _G.PulseESP.ESPFolder then
                                        billboard.Parent = _G.PulseESP.ESPFolder
                                    else
                                        billboard.Parent = workspace
                                    end

                                    _G.PulseESP.characterHighlights[character] = {
                                        highlight = highlight,
                                        billboard = billboard,
                                        nameLabel = nameLabel
                                    }
                                end
                            end
                        end
                    end
                end
            end
        end
    end)

    for character, data in pairs(_G.PulseESP.characterHighlights) do
        if not currentCharacters[character] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.characterHighlights[character] = nil
        end
    end
end



characterSelector = ESPTab:Dropdown({
    Title = "Group Filter",
    Desc = "Select character types",
    Values = scanAvailableCharacterGroups(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedCharacterGroup = tostring(option)
        if _G.PulseESP.characterESPActive then
            updateCharacterESP()
        end
    end
})

-- Advanced Item Detection System
local espItemSection = ESPTab:Section({
    Title = "Item ESP",
    TextXAlignment = "Left",
    TextSize = 17,
})

-- Combat Equipment Tracker
local gunsArmorSelector = ESPTab:Dropdown({
    Title = "Weapon Filter",
    Desc = "Select weapons/armor",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(GUNS_ARMOR_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedGunsArmorESP = tostring(option)
        if _G.PulseESP.gunsArmorESPActive then
            updateGunsArmorESP()
        end
    end
})

local gunsArmorESP = ESPTab:Toggle({
    Title = "Weapons",
    Desc = "Show weapons and armor",
    Icon = "shield",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.gunsArmorESPActive = state

        if state then
            startESPThread("GunsArmorESP", function()
                while _G.PulseESP.gunsArmorESPActive do
                    pcall(updateGunsArmorESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["GunsArmorESP"] then
                task.cancel(_G.PulseESP.activeThreads["GunsArmorESP"])
                _G.PulseESP.activeThreads["GunsArmorESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.gunsArmorHighlights)
        end
    end
})

-- Energy Resource Locator
local fuelSelector = ESPTab:Dropdown({
    Title = "Fuel Filter",
    Desc = "Select fuel items",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(FUEL_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedFuelESP = tostring(option)
        if _G.PulseESP.fuelESPActive then
            updateFuelESP()
        end
    end
})

local fuelESP = ESPTab:Toggle({
    Title = "Fuel",
    Desc = "Show fuel sources",
    Icon = "flame",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.fuelESPActive = state

        if state then
            startESPThread("FuelESP", function()
                while _G.PulseESP.fuelESPActive do
                    pcall(updateFuelESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["FuelESP"] then
                task.cancel(_G.PulseESP.activeThreads["FuelESP"])
                _G.PulseESP.activeThreads["FuelESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.fuelHighlights)
        end
    end
})

-- Survival Nutrition Scanner
local foodSelector = ESPTab:Dropdown({
    Title = "Food Filter",
    Desc = "Select food items",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(FOOD_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedFoodESP = tostring(option)
        if _G.PulseESP.foodESPActive then
            updateFoodESP()
        end
    end
})

local foodESP = ESPTab:Toggle({
    Title = "Food",
    Desc = "Show food items",
    Icon = "apple",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.foodESPActive = state

        if state then
            startESPThread("FoodESP", function()
                while _G.PulseESP.foodESPActive do
                    pcall(updateFoodESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["FoodESP"] then
                task.cancel(_G.PulseESP.activeThreads["FoodESP"])
                _G.PulseESP.activeThreads["FoodESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.foodHighlights)
        end
    end
})

-- Material Component Detector
local craftingSelector = ESPTab:Dropdown({
    Title = "Craft Filter",
    Desc = "Select crafting materials",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(CRAFTING_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedCraftingESP = tostring(option)
        if _G.PulseESP.craftingESPActive then
            updateCraftingESP()
        end
    end
})

local craftingESP = ESPTab:Toggle({
    Title = "Crafting",
    Desc = "Show crafting materials",
    Icon = "wrench",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.craftingESPActive = state

        if state then
            startESPThread("CraftingESP", function()
                while _G.PulseESP.craftingESPActive do
                    pcall(updateCraftingESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["CraftingESP"] then
                task.cancel(_G.PulseESP.activeThreads["CraftingESP"])
                _G.PulseESP.activeThreads["CraftingESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.craftingHighlights)
        end
    end
})

local monsterESP = ESPTab:Toggle({
    Title = "Entities",
    Desc = "Show entities",
    Icon = "skull",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.monsterESPActive = state

        if state then
            startESPThread("MonsterESP", function()
                while _G.PulseESP.monsterESPActive do
                    pcall(updateMonsterESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["MonsterESP"] then
                task.cancel(_G.PulseESP.activeThreads["MonsterESP"])
                _G.PulseESP.activeThreads["MonsterESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.monsterHighlights)
        end
    end
})

local characterESP = ESPTab:Toggle({
    Title = "Characters",
    Desc = "Show NPCs and characters",
    Icon = "users",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.characterESPActive = state

        if state then
            _G.PulseESP.availableCharacterGroups = scanAvailableCharacterGroups()

            startESPThread("CharacterESP", function()
                while _G.PulseESP.characterESPActive do
                    pcall(updateCharacterESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["CharacterESP"] then
                task.cancel(_G.PulseESP.activeThreads["CharacterESP"])
                _G.PulseESP.activeThreads["CharacterESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.characterHighlights)
        end
    end
})

local renderDistanceSlider = ESPTab:Slider({
    Title = "Range",
    Desc = "Max distance for ESP",
    Step = 1000,
    Value = {
        Min = 100,
        Max = 9999999,
        Default = 500,
    },
    Callback = function(value)
        _G.PulseESP.renderDistance = tonumber(value) or 500
    end
})

local espColor = ESPTab:Colorpicker({
    Title = "Color",
    Desc = "ESP highlight color",
    Default = Color3.fromRGB(0, 255, 0),
    Transparency = 0,
    Callback = function(color)
        _G.PulseESP.espColor = color
    end
})

local refreshItemsButton = ESPTab:Button({
    Title = "Refresh Items",
    Desc = "Update available items",
    Callback = function()
        _G.PulseESP.availableItems = scanAvailableItems()
    end
})

local refreshCharacterGroupsButton = ESPTab:Button({
    Title = "Refresh Chars",
    Desc = "Update character types",
    Callback = function()
        _G.PulseESP.availableCharacterGroups = scanAvailableCharacterGroups()
    end
})

local mapESPSection = ESPTab:Section({
    Title = "Map ESP",
    TextXAlignment = "Left",
    TextSize = 17,
})

local landmarkSelector = ESPTab:Dropdown({
    Title = "Landmark Groups",
    Desc = "Select landmark groups",
    Values = scanAvailableLandmarkGroups(),
    Value = {},
    Multi = true,
    AllowNone = true,
    Callback = function(selectedGroups)
        _G.PulseESP.selectedLandmarkGroups = selectedGroups or {}
        if _G.PulseESP.mapESPActive then
            updateMapESP()
        end
    end
})

local mapESP = ESPTab:Toggle({
    Title = "Map ESP",
    Desc = "Show selected landmarks",
    Icon = "map",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.mapESPActive = state

        if state then
            _G.PulseESP.availableLandmarkGroups = scanAvailableLandmarkGroups()

            startESPThread("MapESP", function()
                while _G.PulseESP.mapESPActive do
                    pcall(updateMapESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["MapESP"] then
                task.cancel(_G.PulseESP.activeThreads["MapESP"])
                _G.PulseESP.activeThreads["MapESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.landmarkHighlights)
        end
    end
})

local refreshLandmarksButton = ESPTab:Button({
    Title = "Refresh Map",
    Desc = "Update landmark groups",
    Callback = function()
        pcall(function()
            local newGroups = scanAvailableLandmarkGroups()
            _G.PulseESP.availableLandmarkGroups = newGroups
            if landmarkSelector and landmarkSelector.Refresh then
                landmarkSelector:Refresh(newGroups)
            elseif landmarkSelector and landmarkSelector.Update then
                landmarkSelector:Update(newGroups)
            end
        end)
    end
})

local listCharactersButton = ESPTab:Button({
    Title = "List Items",
    Desc = "Show nearby characters",
    Callback = function()
        local player = game.Players.LocalPlayer
        if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
            return
        end

        local playerPos = player.Character.HumanoidRootPart.Position
        local charactersFolder = workspace:FindFirstChild("Characters")
        if not charactersFolder then
            return
        end

        local characterList = {}
        local totalCount = 0

        pcall(function()
            for _, group in ipairs(charactersFolder:GetChildren()) do
                if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                    local groupName = tostring(group.Name)
                    local groupCount = 0

                    for _, character in ipairs(group:GetChildren()) do
                        if character:IsA("Model") and character.PrimaryPart then
                            local distance = (character.PrimaryPart.Position - playerPos).Magnitude
                            table.insert(characterList, {
                                name = character.Name,
                                group = groupName,
                                distance = math.floor(distance)
                            })
                            groupCount = groupCount + 1
                            totalCount = totalCount + 1
                        end
                    end
                end
            end
        end)

        table.sort(characterList, function(a, b) return a.distance < b.distance end)
    end
})

-- Detection System Management
local espControlSection = ESPTab:Section({
    Title = "ESP Controls",
    TextXAlignment = "Left",
    TextSize = 17,
})

local clearAllESP = ESPTab:Button({
    Title = "Clear All",
    Desc = "Remove all ESP highlights",
    Callback = function()
        clearESPHighlights(_G.PulseESP.itemHighlights)
        clearESPHighlights(_G.PulseESP.playerHighlights)
        clearESPHighlights(_G.PulseESP.monsterHighlights)
        clearESPHighlights(_G.PulseESP.characterHighlights)
        clearESPHighlights(_G.PulseESP.gunsArmorHighlights)
        clearESPHighlights(_G.PulseESP.fuelHighlights)
        clearESPHighlights(_G.PulseESP.foodHighlights)
        clearESPHighlights(_G.PulseESP.craftingHighlights)
        clearESPHighlights(_G.PulseESP.landmarkHighlights)
    end
})

local refreshESPButton = ESPTab:Button({
    Title = "Refresh All",
    Desc = "Refresh all ESP systems",
    Callback = function()
        clearESPHighlights(_G.PulseESP.gunsArmorHighlights)
        clearESPHighlights(_G.PulseESP.fuelHighlights)
        clearESPHighlights(_G.PulseESP.foodHighlights)
        clearESPHighlights(_G.PulseESP.craftingHighlights)
        clearESPHighlights(_G.PulseESP.landmarkHighlights)
    end
})

local function scanAvailableItems()
    local items = {}
    local itemCounts = {}
    local workspaceItems = workspace:FindFirstChild("Items")
    
    if not workspaceItems then return {"No items found"} end
    
    pcall(function()
        for _, item in ipairs(workspaceItems:GetChildren()) do
            if item:IsA("Model") and item.Name and item.Name ~= "" and item.PrimaryPart then
                local itemName = tostring(item.Name)
                local isExcluded = false
                
                for _, excluded in ipairs(_G.PulseItems.excludeList) do
                    if itemName:lower():find(excluded:lower()) then
                        isExcluded = true
                        break
                    end
                end
                
                if not isExcluded then
                    if not itemCounts[itemName] then
                        itemCounts[itemName] = 0
                        table.insert(items, itemName)
                    end
                    itemCounts[itemName] = itemCounts[itemName] + 1
                end
            end
        end
    end)
    
    pcall(function()
        table.sort(items, function(a, b)
            if itemCounts[a] == itemCounts[b] then
                return tostring(a) < tostring(b)
            end
            return itemCounts[a] > itemCounts[b]
        end)
    end)
    
    local result = {}
    for i = 1, math.min(#items, 50) do
        local itemName = items[i]
        local count = itemCounts[itemName]
        table.insert(result, itemName .. " (" .. count .. ")")
    end
    
    return #result > 0 and result or {"No valid items found"}
end

local function startItemThread(name, func)
    if _G.PulseItems.activeThreads[name] then
        task.cancel(_G.PulseItems.activeThreads[name])
    end
    _G.PulseItems.activeThreads[name] = task.spawn(func)
end

local function stopItemThread(name)
    if _G.PulseItems.activeThreads[name] then
        task.cancel(_G.PulseItems.activeThreads[name])
        _G.PulseItems.activeThreads[name] = nil
    end
end

local function bringItemsToPlayer(itemNames, isAllItems)
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local playerPos = player.Character.HumanoidRootPart.Position
    local workspaceItems = workspace:FindFirstChild("Items")
    if not workspaceItems then return end
    
    local itemsMoved = 0
    local maxItems = _G.PulseItems.maxItemsPerBatch
    local targetDistance = _G.PulseItems.bringDistance
    
    local itemsToProcess = {}
    
    for _, item in ipairs(workspaceItems:GetChildren()) do
        if itemsMoved >= maxItems then break end
        
        if item:IsA("Model") and item.PrimaryPart then
            local itemName = tostring(item.Name)
            local shouldBring = false
            
            if isAllItems then
                local isExcluded = false
                for _, excluded in ipairs(_G.PulseItems.excludeList) do
                    if itemName:lower():find(excluded:lower()) then
                        isExcluded = true
                        break
                    end
                end
                shouldBring = not isExcluded
            else
                for _, selectedName in ipairs(itemNames) do
                    local cleanName = selectedName:match("^(.+)%s%(%d+%)$") or selectedName
                    if itemName == cleanName then
                        shouldBring = true
                        break
                    end
                end
            end
            
            if shouldBring then
                local distance = (item.PrimaryPart.Position - playerPos).Magnitude
                if distance > targetDistance then
                    table.insert(itemsToProcess, {item = item, distance = distance})
                end
            end
        end
    end
    
    table.sort(itemsToProcess, function(a, b) return a.distance < b.distance end)
    
    for i = 1, math.min(maxItems, #itemsToProcess) do
        local itemData = itemsToProcess[i]
        local item = itemData.item
        
        pcall(function()
            if item and item.Parent and item.PrimaryPart then
                local targetCFrame = player.Character.HumanoidRootPart.CFrame * CFrame.new(
                    math.random(-5, 5),
                    math.random(2, 5),
                    math.random(-5, 5)
                )
                
                item:PivotTo(targetCFrame)
                itemsMoved = itemsMoved + 1
                task.wait(0.1)
            end
        end)
    end
    
    return itemsMoved
end

local function updateAvailableItems()
    local currentTime = tick()
    if currentTime - _G.PulseItems.lastScanTime > _G.PulseItems.itemScanInterval then
        _G.PulseItems.availableItems = scanAvailableItems()
        _G.PulseItems.lastScanTime = currentTime
    end
end

local itemSystemSection = ItemsTab:Section({
    Title = "Item System",
    TextXAlignment = "Left",
    TextSize = 17,
})

local itemSelector = ItemsTab:Dropdown({
    Title = "Items",
    Desc = "Select items to bring",
    Values = scanAvailableItems(),
    Value = {},
    Multi = true,
    AllowNone = true,
    Callback = function(selectedItems)
        _G.PulseItems.selectedItems = selectedItems or {}
    end
})

local bringSelectedItems = ItemsTab:Toggle({
    Title = "Bring Selected",
    Desc = "Bring selected items",
    Icon = "magnet",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseItems.bringSelectedItems = state
        
        if state then
            startItemThread("BringSelected", function()
                while _G.PulseItems.bringSelectedItems do
                    if #_G.PulseItems.selectedItems > 0 then
                        local moved = bringItemsToPlayer(_G.PulseItems.selectedItems, false)
                    end
                    task.wait(1)
                end
            end)
        else
            stopItemThread("BringSelected")
        end
    end
})

local bringAllItems = ItemsTab:Toggle({
    Title = "Bring All",
    Desc = "Bring all valid items",
    Icon = "box",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseItems.bringAllItems = state
        
        if state then
            startItemThread("BringAll", function()
                while _G.PulseItems.bringAllItems do
                    local moved = bringItemsToPlayer({}, true)
                    task.wait(1.5)
                end
            end)
        else
            stopItemThread("BringAll")
        end
    end
})

local itemControlSection = ItemsTab:Section({
    Title = "Settings",
    TextXAlignment = "Left",
    TextSize = 17,
})

local bringDistance = ItemsTab:Slider({
    Title = "Min Distance",
    Desc = "Only bring items farther than this",
    Step = 5,
    Value = {
        Min = 10,
        Max = 10000,
        Default = 25,
    },
    Callback = function(value)
        _G.PulseItems.bringDistance = tonumber(value) or 25
    end
})

local maxItemsBatch = ItemsTab:Slider({
    Title = "Batch Size",
    Desc = "Max items per cycle",
    Step = 1,
    Value = {
        Min = 5,
        Max = 50,
        Default = 15,
    },
    Callback = function(value)
        _G.PulseItems.maxItemsPerBatch = tonumber(value) or 15
    end
})

local scanInterval = ItemsTab:Slider({
    Title = "Scan Rate",
    Desc = "Seconds between updates",
    Step = 1,
    Value = {
        Min = 1,
        Max = 10,
        Default = 3,
    },
    Callback = function(value)
        _G.PulseItems.itemScanInterval = tonumber(value) or 3
    end
})

local itemUtilitySection = ItemsTab:Section({
    Title = "Utils",
    TextXAlignment = "Left",
    TextSize = 17,
})

local itemStatsSection = ItemsTab:Section({
    Title = "Live Stats",
    TextXAlignment = "Left",
    TextSize = 17,
})

local function generateItemStatsParagraph()
    local workspaceItems = workspace:FindFirstChild("Items")
    if not workspaceItems then 
        return "❌ No Items folder found in workspace!"
    end
    
    local stats = {}
    local totalItems = 0
    
    for _, item in ipairs(workspaceItems:GetChildren()) do
        if item:IsA("Model") and item.Name and item.PrimaryPart then
            local itemName = tostring(item.Name)
            local isExcluded = false
            
            for _, excluded in ipairs(_G.PulseItems.excludeList) do
                if itemName:lower():find(excluded:lower()) then
                    isExcluded = true
                    break
                end
            end
            
            if not isExcluded then
                stats[itemName] = (stats[itemName] or 0) + 1
                totalItems = totalItems + 1
            end
        end
    end
    
    local uniqueTypes = 0
    for _ in pairs(stats) do
        uniqueTypes = uniqueTypes + 1
    end
    
    if totalItems == 0 then
        return "📦 No valid items found in the game world."
    end
    
    local changes = {}
    local newItems = {}
    local lostItems = {}
    
    if _G.PulseItems.previousStats and next(_G.PulseItems.previousStats) then
        for itemName, count in pairs(stats) do
            local prevCount = _G.PulseItems.previousStats[itemName] or 0
            if prevCount == 0 then
                table.insert(newItems, itemName)
            elseif count ~= prevCount then
                changes[itemName] = {current = count, previous = prevCount, diff = count - prevCount}
            end
        end
        
        for itemName, prevCount in pairs(_G.PulseItems.previousStats) do
            if not stats[itemName] then
                table.insert(lostItems, itemName)
            end
        end
    end
    
    _G.PulseItems.previousStats = stats
    
    local sortedStats = {}
    for name, count in pairs(stats) do
        table.insert(sortedStats, {name = name, count = count})
    end
    
    table.sort(sortedStats, function(a, b) return a.count > b.count end)
    
    local paragraphText = "🔄 **LIVE SCAN** - " .. os.date("%H:%M:%S") .. "\n"
    paragraphText = paragraphText .. "📊 " .. totalItems .. " total items | " .. uniqueTypes .. " unique types\n\n"
    
    if #newItems > 0 then
        paragraphText = paragraphText .. "🆕 **NEW**: " .. table.concat(newItems, ", ") .. "\n"
    end
    if #lostItems > 0 then
        paragraphText = paragraphText .. "🗑️ **REMOVED**: " .. table.concat(lostItems, ", ") .. "\n"
    end
    if next(changes) then
        paragraphText = paragraphText .. "📈 **CHANGED**: "
        local changeList = {}
        for itemName, change in pairs(changes) do
            if change.diff > 0 then
                table.insert(changeList, itemName .. " (+" .. change.diff .. ")")
            else
                table.insert(changeList, itemName .. " (" .. change.diff .. ")")
            end
        end
        paragraphText = paragraphText .. table.concat(changeList, ", ") .. "\n"
    end
    
    if #newItems > 0 or #lostItems > 0 or next(changes) then
        paragraphText = paragraphText .. "\n"
    end
    
    paragraphText = paragraphText .. "🏆 **TOP 10 ITEMS**:\n"
    for i = 1, math.min(10, #sortedStats) do
        local item = sortedStats[i]
        local changeIndicator = ""
        if changes[item.name] then
            if changes[item.name].diff > 0 then
                changeIndicator = " 📈+" .. changes[item.name].diff
            elseif changes[item.name].diff < 0 then
                changeIndicator = " 📉" .. changes[item.name].diff
            end
        end
        paragraphText = paragraphText .. i .. ". " .. item.name .. " ×" .. item.count .. changeIndicator .. "\n"
    end
    
    if #sortedStats > 10 then
        paragraphText = paragraphText .. "\n📋 **COMPLETE INVENTORY** (Alphabetical):\n"
        
        local alphabeticalStats = {}
        for i = 1, #sortedStats do
            table.insert(alphabeticalStats, sortedStats[i])
        end
        table.sort(alphabeticalStats, function(a, b) return a.name < b.name end)
        
        local lines = {}
        local currentLine = ""
        
        for i = 1, #alphabeticalStats do
            local item = alphabeticalStats[i]
            local itemText = item.name .. " ×" .. item.count
            
            if currentLine == "" then
                currentLine = "• " .. itemText
            else
                local testLine = currentLine .. " | " .. itemText
                if #testLine > 50 then
                    table.insert(lines, currentLine)
                    currentLine = "• " .. itemText
                else
                    currentLine = testLine
                end
            end
        end
        
        if currentLine ~= "" then
            table.insert(lines, currentLine)
        end
        
        for i = 1, math.min(20, #lines) do
            paragraphText = paragraphText .. lines[i] .. "\n"
        end
        
        if #lines > 20 then
            paragraphText = paragraphText .. "... and " .. (#lines - 20) .. " more lines (use 'Show Item Statistics' button for full list)\n"
        end
    else
        paragraphText = paragraphText .. "\n✅ Complete list shown above"
    end
    
    return paragraphText
end

local itemStatsParagraph = ItemsTab:Paragraph({
    Title = "Current Game Items",
    Desc = generateItemStatsParagraph(),
})

local autoUpdateToggle = ItemsTab:Toggle({
    Title = "Auto Update",
    Desc = "Auto refresh stats every 10s",
    Icon = "refresh-cw",
    Type = "Checkbox",
    Default = true,
    Callback = function(state)
        _G.PulseItems.autoUpdateStats = state
    end
})

local refreshStatsParagraphButton = ItemsTab:Button({
    Title = "Manual Update",
    Desc = "Refresh stats display",
    Callback = function()
        pcall(function()
            local newStats = generateItemStatsParagraph()
            if itemStatsParagraph and itemStatsParagraph.Set then
                itemStatsParagraph:Set("Current Game Items", newStats)
            elseif itemStatsParagraph and itemStatsParagraph.Update then
                itemStatsParagraph:Update("Current Game Items", newStats)
            elseif itemStatsParagraph and itemStatsParagraph.SetDesc then
                itemStatsParagraph:SetDesc(newStats)
            end
        end)
    end
})

local clearStatsHistoryButton = ItemsTab:Button({
    Title = "Clear History",
    Desc = "Reset change tracking",
    Callback = function()
        pcall(function()
            _G.PulseItems.previousStats = {}
            local newStats = generateItemStatsParagraph()
            if itemStatsParagraph and itemStatsParagraph.Set then
                itemStatsParagraph:Set("Current Game Items", newStats)
            elseif itemStatsParagraph and itemStatsParagraph.Update then
                itemStatsParagraph:Update("Current Game Items", newStats)
            elseif itemStatsParagraph and itemStatsParagraph.SetDesc then
                itemStatsParagraph:SetDesc(newStats)
            end
        end)
    end
})

local refreshItemsButton = ItemsTab:Button({
    Title = "Refresh List",
    Desc = "Update items dropdown",
    Callback = function()
        pcall(function()
            local newItems = scanAvailableItems()
            if itemSelector and itemSelector.Refresh then
                itemSelector:Refresh(newItems)
            elseif itemSelector and itemSelector.Update then
                itemSelector:Update(newItems)
            end
        end)
    end
})

local itemStatsButton = ItemsTab:Button({
    Title = "Show Stats",
    Desc = "Show detailed item info",
    Callback = function()
        local workspaceItems = workspace:FindFirstChild("Items")
        if not workspaceItems then 
            return 
        end
        
        local stats = {}
        local totalItems = 0
        
        for _, item in ipairs(workspaceItems:GetChildren()) do
            if item:IsA("Model") and item.Name and item.PrimaryPart then
                local itemName = tostring(item.Name)
                stats[itemName] = (stats[itemName] or 0) + 1
                totalItems = totalItems + 1
            end
        end
        
        local uniqueTypes = 0
        for _ in pairs(stats) do
            uniqueTypes = uniqueTypes + 1
        end
        
        local sortedStats = {}
        for name, count in pairs(stats) do
            table.insert(sortedStats, {name = name, count = count})
        end
        
        table.sort(sortedStats, function(a, b) return a.count > b.count end)
    end
})

local clearNearbyButton = ItemsTab:Button({
    Title = "Clear Nearby",
    Desc = "Remove items within 30 studs",
    Callback = function()
        local player = Players.LocalPlayer
        if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then 
            return 
        end
        
        local playerPos = player.Character.HumanoidRootPart.Position
        local workspaceItems = workspace:FindFirstChild("Items")
        if not workspaceItems then return end
        
        local itemsRemoved = 0
        
        for _, item in ipairs(workspaceItems:GetChildren()) do
            if item:IsA("Model") and item.PrimaryPart then
                local distance = (item.PrimaryPart.Position - playerPos).Magnitude
                if distance <= 30 then
                    pcall(function()
                        item:Destroy()
                        itemsRemoved = itemsRemoved + 1
                    end)
                end
            end
        end
    end
})

local autoRefreshThread = task.spawn(function()
    while true do
        task.wait(10)
        updateAvailableItems()
        if #_G.PulseItems.availableItems > 0 and _G.PulseItems.availableItems[1] ~= "No items found" then
            pcall(function()
                if itemSelector and itemSelector.Refresh then
                    itemSelector:Refresh(_G.PulseItems.availableItems)
                elseif itemSelector and itemSelector.Update then
                    itemSelector:Update(_G.PulseItems.availableItems)
                end
            end)
        end
        
        if _G.PulseItems.autoUpdateStats then
            pcall(function()
                local newStats = generateItemStatsParagraph()
                if itemStatsParagraph and itemStatsParagraph.Set then
                    itemStatsParagraph:Set("Current Game Items", newStats)
                elseif itemStatsParagraph and itemStatsParagraph.Update then
                    itemStatsParagraph:Update("Current Game Items", newStats)
                elseif itemStatsParagraph and itemStatsParagraph.SetDesc then
                    itemStatsParagraph:SetDesc(newStats)
                end
            end)
        end
    end
end)

local uiControlSection = UITab:Section({
    Title = "UI Control",
    TextXAlignment = "Left",
    TextSize = 17,
})

local buildUI = UITab:Toggle({
    Title = "Build",
    Desc = "Show crafting table",
    Icon = "hammer",
    Type = "Checkbox",
    Default = true,
    Callback = function(state)
        pcall(function()
            local craftingTable = game:GetService("Players").LocalPlayer.PlayerGui.Interface.CraftingTable
            if craftingTable then
                craftingTable.Visible = state
            end
        end)
    end
})

local compassUI = UITab:Toggle({
    Title = "Compass",
    Desc = "Show compass",
    Icon = "compass",
    Type = "Checkbox",
    Default = true,
    Callback = function(state)
        pcall(function()
            local compass = game:GetService("Players").LocalPlayer.PlayerGui.Interface.Compass
            if compass then
                compass.Visible = state
            end
        end)
    end
})

local dayUI = UITab:Toggle({
    Title = "Day",
    Desc = "Show day interface",
    Icon = "sun",
    Type = "Checkbox",
    Default = true,
    Callback = function(state)
        pcall(function()
            local dayInterface = game:GetService("Players").LocalPlayer.PlayerGui.Interface.Day
            if dayInterface then
                dayInterface.Visible = state
            end
        end)
    end
})

local function cleanupItemThreads()
    for name, thread in pairs(_G.PulseItems.activeThreads) do
        if thread then
            task.cancel(thread)
        end
    end
    _G.PulseItems.activeThreads = {}
    
    if autoRefreshThread then
        task.cancel(autoRefreshThread)
    end
end

local function cleanupSurvivalThreads()
    for name, thread in pairs(_G.PulseSurvival.activeThreads) do
        if thread then
            task.cancel(thread)
        end
    end
    _G.PulseSurvival.activeThreads = {}
end

local function cleanupCombatThreads()
    for name, thread in pairs(_G.PulseCombat.activeThreads) do
        if thread then
            task.cancel(thread)
        end
    end
    _G.PulseCombat.activeThreads = {}
    
    for character, hitbox in pairs(_G.PulseCombat.extendedHitboxes) do
        pcall(function()
            if hitbox and hitbox.Parent then
                hitbox:Destroy()
            end
        end)
    end
    _G.PulseCombat.extendedHitboxes = {}
end

local function cleanupCameraThreads()
    if _G.PulseCamera.activeThread then
        task.cancel(_G.PulseCamera.activeThread)
        _G.PulseCamera.activeThread = nil
    end
    
    local player = game.Players.LocalPlayer
    if player then
        player.CameraMinZoomDistance = 0.5
        player.CameraMaxZoomDistance = 400
    end
end

local function cleanupProximityThreads()
    if _G.PulseProximity.activeThread then
        task.cancel(_G.PulseProximity.activeThread)
        _G.PulseProximity.activeThread = nil
    end
    
    for prompt, _ in pairs(_G.PulseProximity.modifiedPrompts) do
        restoreProximityPrompt(prompt)
    end
    
    _G.PulseProximity.modifiedPrompts = {}
    _G.PulseProximity.originalValues = {}
end

local function cleanupESPThreads()
    for name, thread in pairs(_G.PulseESP.activeThreads) do
        if thread then
            task.cancel(thread)
        end
    end
    _G.PulseESP.activeThreads = {}
    
    clearESPHighlights(_G.PulseESP.itemHighlights)
    clearESPHighlights(_G.PulseESP.playerHighlights)
    clearESPHighlights(_G.PulseESP.monsterHighlights)
    clearESPHighlights(_G.PulseESP.characterHighlights)
    clearESPHighlights(_G.PulseESP.gunsArmorHighlights)
    clearESPHighlights(_G.PulseESP.fuelHighlights)
    clearESPHighlights(_G.PulseESP.foodHighlights)
    clearESPHighlights(_G.PulseESP.craftingHighlights)
    clearESPHighlights(_G.PulseESP.landmarkHighlights)
end

game:BindToClose(function()
    cleanupItemThreads()
    cleanupSurvivalThreads()
    cleanupCombatThreads()
    cleanupCameraThreads()
    cleanupProximityThreads()
    cleanupESPThreads()
end)

WindUI:Popup({
    Title = "Loaded",
    Icon = "check-circle",
    Content = "Script loaded! Press INSERT to toggle.",
    Buttons = {
        {
            Title = "OK",
            Callback = function()
            end,
            Variant = "Primary",
        }
    }
})

